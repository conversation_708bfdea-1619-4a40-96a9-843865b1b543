-- Generated mock data with comprehensive taggingAttributes coverage
-- Generated at: 2025-07-03T08:34:33.960Z
-- Total images: 20
-- Total attributes covered: 91

-- Insert images
INSERT INTO tagging_image (`url`, `created_at`, `updated_at`) VALUES
('https://picsum.photos/seed/1/400/300', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
('https://picsum.photos/seed/2/400/300', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
('https://picsum.photos/seed/3/400/300', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
('https://picsum.photos/seed/4/400/300', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
('https://picsum.photos/seed/5/400/300', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
('https://picsum.photos/seed/6/400/300', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
('https://picsum.photos/seed/7/400/300', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
('https://picsum.photos/seed/8/400/300', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
('https://picsum.photos/seed/9/400/300', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
('https://picsum.photos/seed/10/400/300', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
('https://picsum.photos/seed/11/400/300', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
('https://picsum.photos/seed/12/400/300', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
('https://picsum.photos/seed/13/400/300', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
('https://picsum.photos/seed/14/400/300', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
('https://picsum.photos/seed/15/400/300', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
('https://picsum.photos/seed/16/400/300', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
('https://picsum.photos/seed/17/400/300', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
('https://picsum.photos/seed/18/400/300', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
('https://picsum.photos/seed/19/400/300', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
('https://picsum.photos/seed/20/400/300', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z');

-- Insert tagging values
INSERT INTO tagging_value (`image_id`, `key`, `value`, `created_at`, `updated_at`) VALUES
(1, 'image_path', 'https://picsum.photos/seed/1/400/300', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Region', 'Chinese', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Gender', 'Male', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Age', '60+', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Hairstyle.Length', 'Very long hair', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Hairstyle.Curl Pattern', 'Wavy', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Hairstyle.Color', 'Natural black', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Hairstyle.Volume', 'Low volume', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Hairstyle.Bangs', 'Sheer bangs', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Hairstyle.Style', 'Waterfall', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Makeup', 'Glamorous shimmer makeup', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Physique', 'Slightly plump', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Skin Tone', 'Fair', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Head Position', 'Head back mouth open', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Model.Model Type', 'Noble', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Length', 'Low-hip length', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Fit', 'Slim fit', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Sleeve Length', 'Extra-long sleeve', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Shoulder Design', 'Drop shoulder', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Material', '丝绸', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Color', '黄色', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Pattern', '几何', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Bottom.Waistline', 'Ultra-low waist', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Bottom.Length', 'Floor-length', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Bottom.Material', '聚酯纤维', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Bottom.Color', '灰色', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Bottom.Pattern', '抽象', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme.Intended Use', 'Social media content', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme.Post-Processing Style', 'Ink painting/oil painting style', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme.Lighting.Light Angle', 'Bottom light', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Neutral White Light', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Normal exposure (Natural)', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Natural light', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Lens Language.Composition', 'Calf-length', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Lens Language.Focal Length Effect', 'Medium telephoto', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Lens Language.Perspective', 'Low angle', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Primary Classification', 'Relaxed & Effortless', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Secondary Classification.Mature & Business', 'Intellectual Style', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Boyish Charm', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Secondary Classification.Sexy', 'Hollywood Golden Age Glamour', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Secondary Classification.Retro & Cultural', 'American Workwear', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Techwear', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Comfort Minimalism', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Chinese Scholar Style', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'American Streetwear', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Boho', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Yohji Yamamoto Style', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Core Features.Color System', '黄色', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Core Features.Material & Texture', '羊毛', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'status', 'pending', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'timestamp', '2025-07-03T08:34:33.957Z', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'summarize', '这是一个总结性的描述文本', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(1, 'total_processing_time', '2025-07-03T08:34:33.958Z', '2025-07-02T09:33:24.317Z', '2025-07-02T09:33:24.317Z'),
(2, 'image_path', 'https://picsum.photos/seed/2/400/300', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Region', 'Spanish', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Gender', 'Male', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Age', '5-9', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Hairstyle.Length', 'Shoulder-length', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Hairstyle.Curl Pattern', 'Loose waves', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Hairstyle.Color', 'Auburn', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Hairstyle.Volume', 'Medium volume', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Hairstyle.Bangs', 'Hime cut', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Hairstyle.Style', 'Waterfall', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Makeup', 'Big-eyed youthful makeup', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Physique', 'Muscular', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Skin Tone', 'Medium', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Head Position', 'Head back mouth open', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Model.Model Type', 'Artistically distinctive model', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Length', 'Low-hip length', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Fit', 'Slim fit', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Sleeve Length', 'Sleeveless', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Shoulder Design', 'Standard shoulder line', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Material', '丝绸', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Color', '绿色', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Pattern', '抽象', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Bottom.Waistline', 'Low waist', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Bottom.Length', 'full-length', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Bottom.Material', '雪纺', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Bottom.Color', '灰色', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Bottom.Pattern', '动物纹', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme.Intended Use', 'Portrait', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme.Post-Processing Style', 'Ink painting/oil painting style', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme.Lighting.Light Angle', 'Direct backlight', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Warm Light', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Low exposure (Dim)', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Natural light', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Lens Language.Composition', 'Full body', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Lens Language.Focal Length Effect', 'Telephoto', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Lens Language.Perspective', 'Frontal', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Primary Classification', 'Retro & Cultural', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Secondary Classification.Mature & Business', 'Intellectual Style', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Changgu Style', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Secondary Classification.Sexy', 'Hollywood Golden Age Glamour', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Secondary Classification.Retro & Cultural', 'British Vintage', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Secondary Classification.Cool & Edgy', 'British High Street', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Korean Style', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Japanese/Korean Fresh Style', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Punk Style', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Secondary Classification.Resort & Vacation', 'French Countryside', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Yohji Yamamoto Style', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Core Features.Color System', '黄色', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Core Features.Material & Texture', '羊毛', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'status', 'completed', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'timestamp', '2025-07-03T08:34:33.958Z', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'summarize', '这是一个总结性的描述文本', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(2, 'total_processing_time', '2025-07-03T08:34:33.958Z', '2025-07-02T17:53:21.927Z', '2025-07-02T17:53:21.927Z'),
(3, 'image_path', 'https://picsum.photos/seed/3/400/300', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Region', 'Japanese', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Gender', 'Female', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Age', '10-14', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Hairstyle.Length', 'Short hair', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Hairstyle.Curl Pattern', 'Wavy', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Hairstyle.Color', 'Auburn', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Hairstyle.Volume', 'High volume', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Hairstyle.Bangs', 'Hime cut', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Hairstyle.Style', 'Braids (Three-strand', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Makeup', 'Glass skin makeup', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Physique', 'Voluptuous', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Skin Tone', 'Tan', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Head Position', 'Chin resting looking sideways', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Model.Model Type', 'Sweet but cool', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Length', 'Calf-length', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Fit', 'Oversized', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Sleeve Length', 'Cap sleeve', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Shoulder Design', 'Standard shoulder line', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Material', '皮革', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Color', '蓝色', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Pattern', '条纹', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Bottom.Waistline', 'High waist', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Bottom.Length', 'Mid-thigh', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Bottom.Material', '针织', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Bottom.Color', '绿色', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Bottom.Pattern', '条纹', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme.Intended Use', 'Runway shot', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme.Post-Processing Style', 'Black & White', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme.Lighting.Light Angle', 'Top light', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Cool Light', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Normal exposure (Natural)', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Mixed lighting', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Lens Language.Composition', 'Mid-thigh', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Lens Language.Focal Length Effect', 'Standard lens', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Lens Language.Perspective', 'Eye-level', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Primary Classification', 'Sunny & Youthful', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Secondary Classification.Mature & Business', 'Maillardcore', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Country Style', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Secondary Classification.Sexy', 'Dark Sexy', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Secondary Classification.Retro & Cultural', 'Japanese Workwear', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Futuristic', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Cleanfit', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Cozy Fit/Lazy Style', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Yuppie', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Mori Girl', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Ann Demeulemeester Style', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Core Features.Color System', '黑色', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Core Features.Material & Texture', '聚酯纤维', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'status', 'in_progress', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'timestamp', '2025-07-03T08:34:33.958Z', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'summarize', '这是一个总结性的描述文本', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(3, 'total_processing_time', '2025-07-03T08:34:33.958Z', '2025-07-02T23:38:01.964Z', '2025-07-02T23:38:01.964Z'),
(4, 'image_path', 'https://picsum.photos/seed/4/400/300', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Region', 'Italian', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Gender', 'Male', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Age', '46-60', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Hairstyle.Length', 'Bald', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Hairstyle.Curl Pattern', 'Tight curls', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Hairstyle.Color', 'Grey', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Hairstyle.Volume', 'High volume', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Hairstyle.Bangs', 'Curved bangs', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Hairstyle.Style', 'Long layered straight hair', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Makeup', 'Y2K makeup', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Physique', 'Voluptuous', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Skin Tone', 'depth (Porcelain', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Head Position', 'Head tilted', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Model.Model Type', 'Intellectual', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Length', 'upper-hip length', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Fit', 'Slim fit', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Sleeve Length', 'Cap sleeve', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Shoulder Design', 'Raglan sleeve', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Material', '皮革', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Color', '灰色', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Pattern', '抽象', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Bottom.Waistline', 'High waist', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Bottom.Length', 'knee-length', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Bottom.Material', '雪纺', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Bottom.Color', '白色', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Bottom.Pattern', '格子', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme.Intended Use', 'Runway shot', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme.Post-Processing Style', 'Light muted elegant feel', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme.Lighting.Light Angle', 'Butterfly light', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Warm Light', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Low exposure (Dim)', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Dimensional light', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Lens Language.Composition', 'Mid-thigh', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Lens Language.Focal Length Effect', 'Medium telephoto', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Lens Language.Perspective', 'Eye-level', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Primary Classification', 'Mature & Business', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Secondary Classification.Mature & Business', 'Business Formal', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Dopamine Style', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Secondary Classification.Sexy', 'Sheer', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Secondary Classification.Retro & Cultural', 'American Vintage', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Dark Avant-garde', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'GorpCore/Outdoor Minimalism', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Cozy Fit/Lazy Style', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Punk Style', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Secondary Classification.Resort & Vacation', 'French Countryside', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'JUUN.J Style', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Core Features.Color System', '灰色', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Core Features.Material & Texture', '皮革', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'status', 'failed', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'timestamp', '2025-07-03T08:34:33.958Z', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'summarize', '这是一个总结性的描述文本', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(4, 'total_processing_time', '2025-07-03T08:34:33.958Z', '2025-07-02T22:26:17.108Z', '2025-07-02T22:26:17.108Z'),
(5, 'image_path', 'https://picsum.photos/seed/5/400/300', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Region', 'British', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Gender', 'Male', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Age', '15-25', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Hairstyle.Length', 'Shoulder-length long hair', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Hairstyle.Curl Pattern', 'Straight', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Hairstyle.Color', 'Shiny', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Hairstyle.Volume', 'Medium volume', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Hairstyle.Bangs', 'Straight-across bangs', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Hairstyle.Style', 'Side part', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Makeup', 'Fox/Snake eye makeup', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Physique', 'Elderly posture', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Skin Tone', 'evenness (Even or uneven)', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Head Position', 'Chin raised', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Model.Model Type', 'Minimalist androgynous', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Length', 'Puddle hem', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Fit', 'Skin-tight', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Sleeve Length', 'Sleeveless', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Shoulder Design', 'Kimono sleeve', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Material', '皮革', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Color', '红色', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Pattern', '纯色', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Bottom.Waistline', 'High waist', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Bottom.Length', 'Puddle hem', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Bottom.Material', '棉', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Bottom.Color', '蓝色', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Bottom.Pattern', '波点', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme.Intended Use', 'Social media content', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme.Post-Processing Style', 'Film grain cinematic feel', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme.Lighting.Light Angle', 'Side backlight', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Cool Light', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Normal exposure (Natural)', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Fluorescent light', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Lens Language.Composition', 'Full body', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Lens Language.Focal Length Effect', 'Telephoto', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Lens Language.Perspective', 'Slight angle', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Primary Classification', 'Dark & Avant-garde', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Secondary Classification.Mature & Business', 'Maillardcore', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Jersey Style', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Secondary Classification.Sexy', 'Classic Red Carpet', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Secondary Classification.Retro & Cultural', 'Classic Qipao/Cheongsam', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Secondary Classification.Cool & Edgy', 'American High Street', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Korean Style', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'New Chinese Style', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Op Art Style', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Secondary Classification.Resort & Vacation', 'French Riviera', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Yohji Yamamoto Style', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Core Features.Color System', '白色', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Core Features.Material & Texture', '丝绸', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'status', 'pending', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'timestamp', '2025-07-03T08:34:33.958Z', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'summarize', '这是一个总结性的描述文本', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(5, 'total_processing_time', '2025-07-03T08:34:33.958Z', '2025-07-02T21:26:35.148Z', '2025-07-02T21:26:35.148Z'),
(6, 'image_path', 'https://picsum.photos/seed/6/400/300', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Region', 'Nordic', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Gender', 'Male', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Age', '36-45', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Hairstyle.Length', 'Very long hair', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Hairstyle.Curl Pattern', 'Loose waves', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Hairstyle.Color', 'Auburn', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Hairstyle.Volume', 'Medium volume', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Hairstyle.Bangs', 'Hime cut', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Hairstyle.Style', 'Korean messy updo', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Makeup', 'Artistic makeup', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Physique', 'Elderly posture', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Skin Tone', 'depth (Porcelain', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Head Position', 'Head down looking sideways', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Model.Model Type', 'Futuristic tech', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Length', 'waist-length', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Fit', 'Oversized', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Sleeve Length', 'Half sleeve', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Shoulder Design', 'Yoke shoulder', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Material', '针织', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Color', '红色', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Pattern', '波点', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Bottom.Waistline', 'High waist', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Bottom.Length', 'ankle-length', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Bottom.Material', '雪纺', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Bottom.Color', '黑色', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Bottom.Pattern', '波点', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme.Intended Use', 'Magazine cover', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme.Post-Processing Style', 'Color mood creation', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme.Lighting.Light Angle', 'Side backlight', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Neutral White Light', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme.Lighting.Exposure Level', 'High exposure (Bright)', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Hard light', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Lens Language.Composition', 'Long shot', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Lens Language.Focal Length Effect', 'Telephoto', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Lens Language.Perspective', 'Frontal', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Primary Classification', 'Dark & Avant-garde', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Secondary Classification.Mature & Business', 'Business Casual', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Jersey Style', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Secondary Classification.Sexy', 'Hot Girl Style', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Secondary Classification.Retro & Cultural', 'Y2K Retro', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Secondary Classification.Cool & Edgy', 'British High Street', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Comfort Minimalism', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Cozy Fit/Lazy Style', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Yuppie', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Island Resort', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Yohji Yamamoto Style', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Core Features.Color System', '黑色', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Core Features.Material & Texture', '针织', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'status', 'pending', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'timestamp', '2025-07-03T08:34:33.958Z', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'summarize', '这是一个总结性的描述文本', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(6, 'total_processing_time', '2025-07-03T08:34:33.958Z', '2025-07-02T21:19:09.925Z', '2025-07-02T21:19:09.925Z'),
(7, 'image_path', 'https://picsum.photos/seed/7/400/300', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Region', 'Egyptian', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Gender', 'Female', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Age', '26-35', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Hairstyle.Length', 'Shoulder-length long hair', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Hairstyle.Curl Pattern', 'Loose waves', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Hairstyle.Color', 'Auburn', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Hairstyle.Volume', 'High volume', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Hairstyle.Bangs', 'Airy bangs', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Hairstyle.Style', 'Long layered straight hair', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Makeup', 'Danish', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Physique', 'Muscular', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Skin Tone', 'Olive)', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Head Position', 'Bent forward head lowered', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Model.Model Type', 'Artistically distinctive model', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Length', 'Low-hip length', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Fit', 'Oversized', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Sleeve Length', 'Sleeveless', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Shoulder Design', 'Kimono sleeve', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Material', '雪纺', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Color', '红色', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Pattern', '波点', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Bottom.Waistline', 'Mid-rise', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Bottom.Length', 'Floor-length', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Bottom.Material', '棉', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Bottom.Color', '灰色', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Bottom.Pattern', '抽象', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme.Intended Use', 'Lifestyle photo', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme.Post-Processing Style', 'Liquid metal distortion', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme.Lighting.Light Angle', 'Side light', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Neutral White Light', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Low exposure (Dim)', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Dimensional light', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Lens Language.Composition', 'Mid-thigh', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Lens Language.Focal Length Effect', 'Telephoto', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Lens Language.Perspective', 'Frontal', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Primary Classification', 'Mature & Business', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Secondary Classification.Mature & Business', 'Business Casual', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Jersey Style', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Secondary Classification.Sexy', 'Classic Red Carpet', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Secondary Classification.Retro & Cultural', 'New Vintage', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Secondary Classification.Cool & Edgy', 'American High Street', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Cleanfit', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Cozy Fit/Lazy Style', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'American Streetwear', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Mori Girl', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Rick Owens Style', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Core Features.Color System', '黑色', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Core Features.Material & Texture', '皮革', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'status', 'in_progress', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'timestamp', '2025-07-03T08:34:33.958Z', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'summarize', '这是一个总结性的描述文本', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(7, 'total_processing_time', '2025-07-03T08:34:33.958Z', '2025-07-02T23:01:03.710Z', '2025-07-02T23:01:03.710Z'),
(8, 'image_path', 'https://picsum.photos/seed/8/400/300', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Region', 'Nordic', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Gender', 'Female', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Age', '15-25', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Hairstyle.Length', 'Shoulder-length', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Hairstyle.Curl Pattern', 'Loose waves', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Hairstyle.Color', 'Matte', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Hairstyle.Volume', 'High volume', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Hairstyle.Bangs', 'Airy bangs', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Hairstyle.Style', 'Xin Zhilei hairstyle', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Makeup', 'Glass skin makeup', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Physique', 'Slightly plump', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Skin Tone', 'Tan', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Head Position', 'Face buried in shoulder (side)', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Model.Model Type', 'Sexy', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Length', 'Calf-length', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Fit', 'Oversized', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Sleeve Length', 'Sleeveless', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Shoulder Design', 'Raglan sleeve', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Material', '聚酯纤维', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Color', '粉色', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Pattern', '纯色', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Bottom.Waistline', 'Ultra-low waist', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Bottom.Length', 'knee-length', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Bottom.Material', '针织', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Bottom.Color', '灰色', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Bottom.Pattern', '波点', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme.Intended Use', 'Runway shot', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme.Post-Processing Style', 'Surreal compositing', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme.Lighting.Light Angle', 'Diffused light', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Neutral White Light', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Low exposure (Dim)', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Fluorescent light', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Lens Language.Composition', 'Full body', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Lens Language.Focal Length Effect', 'Medium telephoto', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Lens Language.Perspective', 'Frontal', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Primary Classification', 'Sexy', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Secondary Classification.Mature & Business', 'Maillardcore', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Preppy Style', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Secondary Classification.Sexy', 'Classic Red Carpet', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Secondary Classification.Retro & Cultural', 'Republican Era Boudoir Style', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Techwear', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Comfort Minimalism', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Chinese Scholar Style', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Hippie', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Mori Girl', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Ann Demeulemeester Style', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Core Features.Color System', '黑色', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Core Features.Material & Texture', '羊毛', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'status', 'in_progress', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'summarize', '这是一个总结性的描述文本', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(8, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-02T20:45:21.288Z', '2025-07-02T20:45:21.288Z'),
(9, 'image_path', 'https://picsum.photos/seed/9/400/300', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Region', 'Sub-Saharan African', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Gender', 'Female', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Age', '60+', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Hairstyle.Length', 'Waist-length long hair', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Hairstyle.Curl Pattern', 'Wavy', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Hairstyle.Color', 'Dark brown', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Hairstyle.Volume', 'Low volume', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Hairstyle.Bangs', 'Side-swept bangs', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Hairstyle.Style', 'Very high', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Makeup', 'English girl', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Physique', 'Tall', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Skin Tone', 'undertone (Warm: Yellow/Golden; Cool: Pink/Blue; Neutral)', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Head Position', 'Head up looking sideways', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Model.Model Type', 'Workplace', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Length', 'upper-hip length', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Fit', 'Skin-tight', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Sleeve Length', 'Cap sleeve', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Shoulder Design', 'Standard shoulder line', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Material', '雪纺', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Color', '蓝色', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Pattern', '条纹', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Bottom.Waistline', 'Mid-rise', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Bottom.Length', 'full-length', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Bottom.Material', '皮革', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Bottom.Color', '黑色', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Bottom.Pattern', '纯色', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme.Intended Use', 'Poster', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme.Post-Processing Style', 'Soft matte texture', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme.Lighting.Light Angle', 'Loop light', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Cool Light', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Low exposure (Dim)', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Natural light', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Lens Language.Composition', 'Mid-thigh', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Lens Language.Focal Length Effect', 'Medium telephoto', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Lens Language.Perspective', 'Low angle', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Primary Classification', 'Retro & Cultural', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Secondary Classification.Mature & Business', 'Intellectual Style', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Dopamine Style', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Secondary Classification.Sexy', 'Lolita Style', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Secondary Classification.Retro & Cultural', 'American Workwear', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Secondary Classification.Cool & Edgy', 'American High Street', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Cleanfit', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'French Romantic', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Grunge', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Italian Dolce Vita', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Ann Demeulemeester Style', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Core Features.Color System', '黑色', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Core Features.Material & Texture', '牛仔布', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'status', 'failed', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'summarize', '这是一个总结性的描述文本', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(9, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-03T00:39:05.930Z', '2025-07-03T00:39:05.930Z'),
(10, 'image_path', 'https://picsum.photos/seed/10/400/300', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Region', 'American', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Gender', 'Male', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Age', '15-25', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Hairstyle.Length', 'Shoulder-length long hair', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Hairstyle.Curl Pattern', 'Straight', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Hairstyle.Color', 'Dark brown', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Hairstyle.Volume', 'Medium volume', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Hairstyle.Bangs', 'Straight-across bangs', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Hairstyle.Style', 'Medium-length slicked back', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Makeup', 'Apathetic tired makeup', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Physique', 'Even', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Skin Tone', 'Fair', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Head Position', 'Head down glancing sideways', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Model.Model Type', 'Punk', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Length', 'Above-waist-length', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Fit', 'Super oversized', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Sleeve Length', 'Sleeveless', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Shoulder Design', 'Raglan sleeve', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Material', '雪纺', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Color', '蓝色', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Pattern', '几何', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Bottom.Waistline', 'Ultra-low waist', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Bottom.Length', 'Mid-thigh', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Bottom.Material', '针织', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Bottom.Color', '灰色', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Bottom.Pattern', '波点', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme.Intended Use', 'Lifestyle photo', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme.Post-Processing Style', 'Film simulation', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme.Lighting.Light Angle', 'Butterfly light', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Neutral White Light', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Low exposure (Dim)', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Dimensional light', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Lens Language.Composition', 'Half body', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Lens Language.Focal Length Effect', 'Standard lens', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Lens Language.Perspective', 'Back:', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Primary Classification', 'Sexy', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Secondary Classification.Mature & Business', 'Business Street Style', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Jersey Style', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Secondary Classification.Sexy', 'Hollywood Golden Age Glamour', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Secondary Classification.Retro & Cultural', 'Y2K Retro', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Wasteland/Dystopian', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Cleanfit', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Chinese Scholar Style', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Rock Style', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Mori Girl', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'JUUN.J Style', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Core Features.Color System', '灰色', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Core Features.Material & Texture', '针织', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'status', 'pending', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'summarize', '这是一个总结性的描述文本', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(10, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-03T03:19:16.877Z', '2025-07-03T03:19:16.877Z'),
(11, 'image_path', 'https://picsum.photos/seed/11/400/300', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Region', 'Japanese', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Gender', 'Male', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Age', '3-5', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Hairstyle.Length', 'Collarbone-length midi', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Hairstyle.Curl Pattern', 'Straight', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Hairstyle.Color', 'Blonde', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Hairstyle.Volume', 'Low volume', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Hairstyle.Bangs', 'Straight-across bangs', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Hairstyle.Style', 'Qing dynasty Liangbatou', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Makeup', 'Apathetic tired makeup', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Physique', 'Even', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Skin Tone', 'Dark', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Head Position', 'Hat pulled low', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Model.Model Type', 'Sweet but cool', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Length', 'upper-hip length', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Fit', 'Slim fit', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Sleeve Length', 'Half sleeve', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Shoulder Design', 'No shoulder seam design', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Material', '聚酯纤维', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Color', '白色', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Pattern', '纯色', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Bottom.Waistline', 'Mid-rise', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Bottom.Length', 'full-length', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Bottom.Material', '聚酯纤维', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Bottom.Color', '蓝色', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Bottom.Pattern', '波点', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme.Intended Use', 'Portrait', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme.Post-Processing Style', 'Low saturation grainy feel', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme.Lighting.Light Angle', 'Bottom light', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Neutral White Light', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Normal exposure (Natural)', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Fluorescent light', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Lens Language.Composition', 'Half body', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Lens Language.Focal Length Effect', 'Standard lens', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Lens Language.Perspective', 'Eye-level', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Primary Classification', 'Dark & Avant-garde', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Secondary Classification.Mature & Business', 'Maillardcore', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Dopamine Style', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Secondary Classification.Sexy', 'Classic Red Carpet', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Secondary Classification.Retro & Cultural', 'British Vintage', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Futuristic', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Cleanfit', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Athflow', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Grunge', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Boho', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Yohji Yamamoto Style', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Core Features.Color System', '白色', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Core Features.Material & Texture', '聚酯纤维', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'status', 'in_progress', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'summarize', '这是一个总结性的描述文本', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(11, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-02T17:55:17.039Z', '2025-07-02T17:55:17.039Z'),
(12, 'image_path', 'https://picsum.photos/seed/12/400/300', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Region', 'Japanese', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Gender', 'Male', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Age', '46-60', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Hairstyle.Length', 'Short hair', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Hairstyle.Curl Pattern', 'Straight', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Hairstyle.Color', 'Natural black', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Hairstyle.Volume', 'Low volume', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Hairstyle.Bangs', 'Hime cut', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Hairstyle.Style', 'Side)', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Makeup', 'Thai', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Physique', 'Slightly plump', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Skin Tone', 'evenness (Even or uneven)', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Head Position', 'Leaning sideways', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Model.Model Type', 'Punk', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Length', 'Mid-thigh', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Fit', 'Super oversized', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Sleeve Length', 'Three-quarter sleeve', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Shoulder Design', 'Standard shoulder line', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Material', '针织', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Color', '红色', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Pattern', '几何', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Bottom.Waistline', 'Ultra-low waist', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Bottom.Length', 'Puddle hem', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Bottom.Material', '棉', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Bottom.Color', '粉色', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Bottom.Pattern', '花卉', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme.Intended Use', 'Poster', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme.Post-Processing Style', 'Surreal compositing', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme.Lighting.Light Angle', 'Side light', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Neutral White Light', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Low exposure (Dim)', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Mixed lighting', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Lens Language.Composition', 'Calf-length', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Lens Language.Focal Length Effect', 'Standard lens', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Lens Language.Perspective', 'Back:', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Primary Classification', 'Relaxed & Effortless', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Secondary Classification.Mature & Business', 'Business Street Style', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Retro Sporty', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Secondary Classification.Sexy', 'Hollywood Golden Age Glamour', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Secondary Classification.Retro & Cultural', 'Japanese Vintage', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Secondary Classification.Cool & Edgy', 'British High Street', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Korean Style', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'French Romantic', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Rock Style', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Secondary Classification.Resort & Vacation', 'French Riviera', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Yohji Yamamoto Style', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Core Features.Color System', '灰色', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Core Features.Material & Texture', '牛仔布', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'status', 'pending', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'summarize', '这是一个总结性的描述文本', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(12, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-02T09:35:33.559Z', '2025-07-02T09:35:33.559Z'),
(13, 'image_path', 'https://picsum.photos/seed/13/400/300', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Region', 'Korean', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Gender', 'Male', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Age', '26-35', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Hairstyle.Length', 'Shoulder-length long hair', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Hairstyle.Curl Pattern', 'Tight curls', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Hairstyle.Color', 'Bleached color', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Hairstyle.Volume', 'Medium volume', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Hairstyle.Bangs', 'Curved bangs', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Hairstyle.Style', 'Waterfall', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Makeup', 'Bayonetta makeup', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Physique', 'Tall', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Skin Tone', 'Dark', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Head Position', 'Head down glancing sideways', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Model.Model Type', 'Punk', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Length', 'Breaking slightly', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Fit', 'Slim fit', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Sleeve Length', 'Half sleeve', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Shoulder Design', 'Yoke shoulder', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Material', '雪纺', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Color', '黄色', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Pattern', '格子', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Bottom.Waistline', 'Low waist', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Bottom.Length', 'Calf-length', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Bottom.Material', '牛仔布', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Bottom.Color', '灰色', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Bottom.Pattern', '几何', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme.Intended Use', 'Runway shot', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme.Post-Processing Style', 'Black & White', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme.Lighting.Light Angle', 'Butterfly light', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Cool Light', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Low exposure (Dim)', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Dimensional light', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Lens Language.Composition', 'Bust up', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Lens Language.Focal Length Effect', 'Medium telephoto', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Lens Language.Perspective', 'Back:', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Primary Classification', 'Sexy', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Secondary Classification.Mature & Business', 'Business Street Style', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Country Style', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Secondary Classification.Sexy', 'Hollywood Golden Age Glamour', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Secondary Classification.Retro & Cultural', 'Barn Style', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Wasteland/Dystopian', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Comfort Minimalism', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'French Romantic', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Yuppie', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Secondary Classification.Resort & Vacation', 'French Riviera', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'JUUN.J Style', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Core Features.Color System', '灰色', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Core Features.Material & Texture', '聚酯纤维', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'status', 'completed', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'summarize', '这是一个总结性的描述文本', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(13, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-02T21:27:05.171Z', '2025-07-02T21:27:05.171Z'),
(14, 'image_path', 'https://picsum.photos/seed/14/400/300', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Region', 'Chinese', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Gender', 'Male', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Age', '46-60', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Hairstyle.Length', 'Pixie cut', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Hairstyle.Curl Pattern', 'Wavy', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Hairstyle.Color', 'Natural black', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Hairstyle.Volume', 'Low volume', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Hairstyle.Bangs', 'Side-swept bangs', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Hairstyle.Style', 'Bowl cut', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Makeup', 'Spring warm tones', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Physique', 'Slightly plump', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Skin Tone', 'Olive)', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Head Position', 'Facing forward', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Model.Model Type', 'Punk', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Length', 'Above-waist-length', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Fit', 'Oversized', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Sleeve Length', 'Short sleeve', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Shoulder Design', 'No shoulder seam design', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Material', '雪纺', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Color', '黄色', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Pattern', '波点', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Bottom.Waistline', 'Mid-rise', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Bottom.Length', 'full-length', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Bottom.Material', '皮革', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Bottom.Color', '黑色', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Bottom.Pattern', '条纹', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme.Intended Use', 'Lifestyle photo', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme.Post-Processing Style', 'High saturation strong contrast intense feel', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme.Lighting.Light Angle', 'Direct backlight', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Neutral White Light', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Low exposure (Dim)', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Dimensional light', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Lens Language.Composition', 'Calf-length', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Lens Language.Focal Length Effect', 'Standard lens', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Lens Language.Perspective', 'Eye-level', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Primary Classification', 'Artistic & Subcultural', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Secondary Classification.Mature & Business', 'Business Formal', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Dopamine Style', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Secondary Classification.Sexy', 'Henley Style', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Secondary Classification.Retro & Cultural', 'European Court Style', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Wasteland/Dystopian', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'GorpCore/Outdoor Minimalism', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Chinese Scholar Style', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Hippie', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Mori Girl', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'JUUN.J Style', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Core Features.Color System', '红色', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Core Features.Material & Texture', '皮革', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'status', 'pending', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'summarize', '这是一个总结性的描述文本', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(14, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-02T20:08:04.410Z', '2025-07-02T20:08:04.410Z'),
(15, 'image_path', 'https://picsum.photos/seed/15/400/300', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Region', 'Sub-Saharan African', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Gender', 'Female', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Age', '10-14', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Hairstyle.Length', 'Short hair', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Hairstyle.Curl Pattern', 'Loose waves', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Hairstyle.Color', 'Auburn', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Hairstyle.Volume', 'Medium volume', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Hairstyle.Bangs', 'Straight-across bangs', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Hairstyle.Style', 'Mohawk', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Makeup', 'Black cat makeup', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Physique', 'Voluptuous', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Skin Tone', 'undertone (Warm: Yellow/Golden; Cool: Pink/Blue; Neutral)', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Head Position', 'Looking sideways', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Model.Model Type', 'Gothic', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Length', 'Floor-length', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Fit', 'Slim fit', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Sleeve Length', 'Extra-long sleeve', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Shoulder Design', 'Raglan sleeve', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Material', '雪纺', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Color', '灰色', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Pattern', '动物纹', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Bottom.Waistline', 'Low waist', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Bottom.Length', 'ankle-length', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Bottom.Material', '雪纺', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Bottom.Color', '蓝色', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Bottom.Pattern', '纯色', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme.Intended Use', 'Poster', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme.Post-Processing Style', 'Film simulation', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme.Lighting.Light Angle', 'Contour light', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Cool Light', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme.Lighting.Exposure Level', 'High exposure (Bright)', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Mixed lighting', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Lens Language.Composition', 'Calf-length', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Lens Language.Focal Length Effect', 'Standard lens', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Lens Language.Perspective', 'Frontal', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Primary Classification', 'Resort & Vacation', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Secondary Classification.Mature & Business', 'Korean Style', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Dopamine Style', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Secondary Classification.Sexy', 'Hot Girl Style', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Secondary Classification.Retro & Cultural', 'American Vintage', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Futuristic', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Korean Style', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Chinese Scholar Style', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'American Streetwear', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Secondary Classification.Resort & Vacation', 'French Riviera', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Ann Demeulemeester Style', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Core Features.Color System', '黄色', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Core Features.Material & Texture', '皮革', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'status', 'completed', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'summarize', '这是一个总结性的描述文本', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(15, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-03T03:04:07.264Z', '2025-07-03T03:04:07.264Z'),
(16, 'image_path', 'https://picsum.photos/seed/16/400/300', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Region', 'Romani', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Gender', 'Male', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Age', '26-35', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Hairstyle.Length', 'Pixie cut', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Hairstyle.Curl Pattern', 'Loose waves', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Hairstyle.Color', 'Bleached color', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Hairstyle.Volume', 'High volume', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Hairstyle.Bangs', 'Curved bangs', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Hairstyle.Style', 'Japanese samurai hairstyle)', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Makeup', 'Asian baddie makeup', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Physique', 'Slightly plump', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Skin Tone', 'Dark', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Head Position', 'Facing forward', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Model.Model Type', 'Gothic', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Length', 'Floor-length', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Fit', 'Slim fit', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Sleeve Length', 'Long sleeve', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Shoulder Design', 'Standard shoulder line', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Material', '聚酯纤维', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Color', '白色', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Pattern', '格子', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Bottom.Waistline', 'Ultra-low waist', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Bottom.Length', 'Floor-length', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Bottom.Material', '针织', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Bottom.Color', '粉色', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Bottom.Pattern', '动物纹', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme.Intended Use', 'Magazine cover', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme.Post-Processing Style', 'Liquid metal distortion', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme.Lighting.Light Angle', 'Contour light', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Cool Light', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Normal exposure (Natural)', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Fluorescent light', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Lens Language.Composition', 'Bust up', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Lens Language.Focal Length Effect', 'Medium telephoto', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Lens Language.Perspective', 'Frontal', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Primary Classification', 'Retro & Cultural', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Secondary Classification.Mature & Business', 'Old Money', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Jersey Style', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Secondary Classification.Sexy', 'Sheer', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Secondary Classification.Retro & Cultural', 'European Court Style', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Dark Avant-garde', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Cleanfit', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Japanese/Korean Fresh Style', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Grunge', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Mori Girl', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'JUUN.J Style', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Core Features.Color System', '白色', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Core Features.Material & Texture', '雪纺', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'status', 'failed', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'summarize', '这是一个总结性的描述文本', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(16, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-02T14:26:25.293Z', '2025-07-02T14:26:25.293Z'),
(17, 'image_path', 'https://picsum.photos/seed/17/400/300', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Region', 'Māori', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Gender', 'Female', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Age', '0-3', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Hairstyle.Length', 'Shoulder-length', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Hairstyle.Curl Pattern', 'Loose waves', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Hairstyle.Color', 'Dark brown', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Hairstyle.Volume', 'Low volume', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Hairstyle.Bangs', 'Curved bangs', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Hairstyle.Style', 'Greek goddess knot', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Makeup', 'Asian baddie makeup', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Physique', 'Muscular', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Skin Tone', 'undertone (Warm: Yellow/Golden; Cool: Pink/Blue; Neutral)', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Head Position', 'Facing forward', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Model.Model Type', 'Workplace', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Length', 'Mid-thigh', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Fit', 'Slim fit', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Sleeve Length', 'Extra-long sleeve', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Shoulder Design', 'Kimono sleeve', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Material', '皮革', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Color', '灰色', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Pattern', '花卉', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Bottom.Waistline', 'Low waist', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Bottom.Length', 'knee-length', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Bottom.Material', '羊毛', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Bottom.Color', '灰色', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Bottom.Pattern', '几何', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme.Intended Use', 'Product lookbook', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme.Post-Processing Style', 'Light muted elegant feel', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme.Lighting.Light Angle', 'Diffused light', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Warm Light', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Normal exposure (Natural)', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Fluorescent light', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Lens Language.Composition', 'Long shot', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Lens Language.Focal Length Effect', 'Medium telephoto', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Lens Language.Perspective', 'Profile', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Primary Classification', 'Minimalist & Sharp', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Secondary Classification.Mature & Business', 'Business Formal', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Preppy Style', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Secondary Classification.Sexy', 'DSQUARED2 Style', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Secondary Classification.Retro & Cultural', 'Japanese Vintage', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Techwear', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Comfort Minimalism', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Chinese Scholar Style', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Gothic', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Italian Dolce Vita', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Rick Owens Style', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Core Features.Color System', '粉色', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Core Features.Material & Texture', '雪纺', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'status', 'pending', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'summarize', '这是一个总结性的描述文本', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(17, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-02T22:46:04.810Z', '2025-07-02T22:46:04.810Z'),
(18, 'image_path', 'https://picsum.photos/seed/18/400/300', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Region', 'French', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Gender', 'Female', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Age', '60+', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Hairstyle.Length', 'Shoulder-length', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Hairstyle.Curl Pattern', 'Straight', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Hairstyle.Color', 'Shiny', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Hairstyle.Volume', 'High volume', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Hairstyle.Bangs', 'None', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Hairstyle.Style', 'Precise bob', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Makeup', 'Tomie makeup', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Physique', 'Obese', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Skin Tone', 'Fair', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Head Position', 'Facing forward', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Model.Model Type', 'Sporty', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Length', 'Above-waist-length', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Fit', 'Skin-tight', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Sleeve Length', 'Extra-long sleeve', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Shoulder Design', 'No shoulder seam design', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Material', '羊毛', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Color', '粉色', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Pattern', '抽象', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Bottom.Waistline', 'Ultra-low waist', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Bottom.Length', 'full-length', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Bottom.Material', '皮革', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Bottom.Color', '蓝色', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Bottom.Pattern', '纯色', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme.Intended Use', 'Advertisement', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme.Post-Processing Style', 'Black & White', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme.Lighting.Light Angle', 'Loop light', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Neutral White Light', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme.Lighting.Exposure Level', 'High exposure (Bright)', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Mixed lighting', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Lens Language.Composition', 'Calf-length', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Lens Language.Focal Length Effect', 'Standard lens', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Lens Language.Perspective', 'Frontal', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Primary Classification', 'Sexy', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Secondary Classification.Mature & Business', 'Old Money', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Boyish Charm', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Secondary Classification.Sexy', 'DSQUARED2 Style', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Secondary Classification.Retro & Cultural', 'Yama Style', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Secondary Classification.Cool & Edgy', 'American High Street', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'GorpCore/Outdoor Minimalism', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'New Chinese Style', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Hip-Hop Style', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Mori Girl', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Ann Demeulemeester Style', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Core Features.Color System', '黑色', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Core Features.Material & Texture', '雪纺', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'status', 'in_progress', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'summarize', '这是一个总结性的描述文本', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(18, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-03T05:00:48.306Z', '2025-07-03T05:00:48.306Z'),
(19, 'image_path', 'https://picsum.photos/seed/19/400/300', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Region', 'French', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Gender', 'Male', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Age', '36-45', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Hairstyle.Length', 'Waist-length long hair', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Hairstyle.Curl Pattern', 'Tight curls', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Hairstyle.Color', 'Shiny', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Hairstyle.Volume', 'Medium volume', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Hairstyle.Bangs', 'Side-swept bangs', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Hairstyle.Style', 'Medium-length slicked back', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Makeup', 'Bayonetta makeup', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Physique', 'Voluptuous', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Skin Tone', 'Fair', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Head Position', 'Mid-hair flip', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Model.Model Type', 'Student', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Length', 'Breaking slightly', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Fit', 'Super oversized', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Sleeve Length', 'Short sleeve', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Shoulder Design', 'No shoulder seam design', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Material', '棉', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Color', '白色', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Pattern', '格子', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Bottom.Waistline', 'Ultra-low waist', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Bottom.Length', 'Breaking slightly', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Bottom.Material', '丝绸', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Bottom.Color', '黄色', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Bottom.Pattern', '花卉', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme.Intended Use', 'Product lookbook', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme.Post-Processing Style', 'Color mood creation', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme.Lighting.Light Angle', 'Diffused light', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Neutral White Light', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Low exposure (Dim)', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Soft light', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Lens Language.Composition', 'Calf-length', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Lens Language.Focal Length Effect', 'Telephoto', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Lens Language.Perspective', 'Slight angle', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Primary Classification', 'Minimalist & Sharp', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Secondary Classification.Mature & Business', 'Intellectual Style', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Jersey Style', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Secondary Classification.Sexy', 'Classic Red Carpet', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Secondary Classification.Retro & Cultural', 'Traditional Chinese', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Dark Avant-garde', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Cleanfit', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Athflow', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Grunge', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Boho', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'JUUN.J Style', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Core Features.Color System', '灰色', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Core Features.Material & Texture', '羊毛', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'status', 'completed', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'timestamp', '2025-07-03T08:34:33.959Z', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'summarize', '这是一个总结性的描述文本', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(19, 'total_processing_time', '2025-07-03T08:34:33.959Z', '2025-07-02T14:25:25.826Z', '2025-07-02T14:25:25.826Z'),
(20, 'image_path', 'https://picsum.photos/seed/20/400/300', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Region', 'Egyptian', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Gender', 'Female', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Age', '46-60', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Hairstyle', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Hairstyle.Length', 'Collarbone-length midi', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Hairstyle.Curl Pattern', 'Loose waves', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Hairstyle.Color', 'Grey', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Hairstyle.Volume', 'Medium volume', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Hairstyle.Bangs', 'Straight-Across bangs', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Hairstyle.Style', 'Flower bun', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Makeup', 'African', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Gaze Expression', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Physique', 'Voluptuous', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Skin Tone', 'Olive)', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Posture', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Head Position', 'Chin resting looking sideways', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Model.Model Type', 'Sickly chic', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Style', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Length', 'Mid-thigh', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Fit', 'Oversized', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Sleeve Length', 'Half sleeve', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Shoulder Design', 'Standard shoulder line', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Hemline', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Neckline', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Sleeve Type', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Pockets', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Embellishments', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Material', '皮革', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Color', '粉色', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Pattern', '动物纹', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Top.Garment Feature', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Bottom', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Bottom.Style', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Bottom.Waistline', 'High waist', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Bottom.Length', 'full-length', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Bottom.Hemline', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Bottom.Pockets', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Bottom.Embellishments', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Bottom.Material', '羊毛', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Bottom.Color', '粉色', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Bottom.Pattern', '几何', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Bottom.Garment Feature', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Layering Piece', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Shoes', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Accessories', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Clothing.Other Significant Details', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme.Intended Use', 'Poster', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme.Shooting Scene', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme.Post-Processing Style', 'Clean crisp clarity', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme.Lighting', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme.Lighting.Light Angle', 'Bottom light', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme.Lighting.Color Temperature', 'Warm Light', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme.Lighting.Exposure Level', 'Low exposure (Dim)', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme.Lighting.Atmosphere', 'Flat light', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme.Watermark Analysis', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme.Watermark Analysis.Existence', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Shooting Theme.Watermark Analysis.Specific Content', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Lens Language', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Lens Language.Composition', 'Long shot', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Lens Language.Focal Length Effect', 'Medium telephoto', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Lens Language.Perspective', 'High angle', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Primary Classification', 'Artistic & Subcultural', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Secondary Classification', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Secondary Classification.Mature & Business', 'Intellectual Style', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Secondary Classification.Sunny & Youthful', 'Retro Sporty', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Secondary Classification.Sexy', 'Dark Sexy', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Secondary Classification.Retro & Cultural', 'Republican Era Boudoir Style', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Secondary Classification.Cool & Edgy', 'Futuristic', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Secondary Classification.Minimalist & Sharp', 'Cleanfit', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Secondary Classification.Relaxed & Effortless', 'Cozy Fit/Lazy Style', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Secondary Classification.Artistic & Subcultural', 'Punk Style', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Secondary Classification.Resort & Vacation', 'Boho', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Secondary Classification.Dark & Avant-garde', 'Ann Demeulemeester Style', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Core Features', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Core Features.Color System', '黑色', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Core Features.Cut & Silhouette', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Core Features.Material & Texture', '针织', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Core Features.Scene Atmosphere', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Core Features.Signature Elements', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'analysis.Style.Overall Image Description', '这是一个详细的分析描述', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'status', 'completed', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'timestamp', '2025-07-03T08:34:33.960Z', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'summarize', '这是一个总结性的描述文本', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z'),
(20, 'total_processing_time', '2025-07-03T08:34:33.960Z', '2025-07-02T23:26:26.952Z', '2025-07-02T23:26:26.952Z');