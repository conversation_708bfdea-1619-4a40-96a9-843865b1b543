import Navbar from '@/components/layout/Navbar'
import { Outlet, createRootRoute, useLocation } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'

// Layout component that will be used by all routes
const RootLayout = () => {
  const location = useLocation()
  const isImageDetailPage = location.pathname.startsWith('/images/')

  return (
    <div className="bg-background text-foreground min-h-screen flex flex-col">
      <Navbar />
      {isImageDetailPage ? (
        // 图片详情页面使用全屏布局
        <div className="flex-1 overflow-hidden">
          <Outlet />
        </div>
      ) : (
        // 其他页面使用容器布局
        <div className="container mx-auto p-8">
          <Outlet />
        </div>
      )}
      <TanStackRouterDevtools />
    </div>
  )
}

export const Route = createRootRoute({
  component: RootLayout,
})
