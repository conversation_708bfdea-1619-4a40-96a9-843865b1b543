import { Button } from '@/components/ui/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table';
import { taggingAttributes, type TaggingImage, type TaggingValue } from '@/types';
import { createFileRoute, useLoaderData } from '@tanstack/react-router';
import { ChevronDown, ChevronRight } from 'lucide-react';
import * as React from 'react';

interface ImageWithTagging extends TaggingImage {
  tagging?: Omit<TaggingValue, 'id' | 'image_id' | 'created_at' | 'updated_at'>[];
}

export const Route = createFileRoute('/images/$id')({ 
  loader: async ({ params }) => {
    const id = Number(params.id);
    
    // 模拟获取图片数据
    const image: ImageWithTagging = {
      id,
      url: `https://picsum.photos/seed/${id}/400/300`,
      created_at: new Date(),
      updated_at: new Date(),
      deleted_at: null,
      // 模拟一些标注数据
      tagging: [
        { key: 'image_path', value: `https://picsum.photos/seed/${id}/400/300` },
        { key: 'analysis.Model.Gender', value: 'Female' },
        { key: 'analysis.Model.Age', value: '26-35' },
        { key: 'analysis.Model.Region', value: 'American' },
      ]
    };

    return { image, taggingAttributes };
  },
  component: ImageDetail,
});

// 定义树节点类型
interface AttributeNode {
  key: string;
  name: string;
  displayName: string;
  enumerations: string[];
  displayEnumerations: string[];
  children?: AttributeNode[];
  isLeaf: boolean;
  path: string;
  disabled?: boolean;
}

// 需要禁用的键列表
const DISABLED_KEYS = ['image_path', 'status', 'timestamp', 'summarize', 'total_processing_time'];

function buildAttributeTree(attributes: typeof taggingAttributes): AttributeNode[] {
  const rootNodes: AttributeNode[] = [];
  const nodeMap: Record<string, AttributeNode> = {};

  // 首先创建所有节点
  attributes.forEach(attr => {
    const path = attr.key;
    const segments = path.split('.');
    const isLeaf = !attributes.some(a => a.key !== path && a.key.startsWith(path + '.'));
    const disabled = DISABLED_KEYS.includes(attr.key);
    
    const node: AttributeNode = {
      key: attr.key,
      name: attr.name,
      displayName: attr.displayName,
      enumerations: attr.enumerations,
      displayEnumerations: attr.displayEnumerations,
      children: [],
      isLeaf,
      path,
      disabled
    };
    
    nodeMap[path] = node;
    
    // 如果是根节点，直接添加到结果中
    if (segments.length === 1) {
      rootNodes.push(node);
    }
  });
  
  // 然后构建树结构
  attributes.forEach(attr => {
    const path = attr.key;
    const segments = path.split('.');
    
    if (segments.length > 1) {
      // 找到父节点
      const parentPath = segments.slice(0, segments.length - 1).join('.');
      const parentNode = nodeMap[parentPath];
      
      if (parentNode) {
        parentNode.children = parentNode.children || [];
        parentNode.children.push(nodeMap[path]);
      } else {
        // 如果找不到父节点，作为根节点处理
        rootNodes.push(nodeMap[path]);
      }
    }
  });
  
  return rootNodes;
}

function ImageDetail() {
  const { image, taggingAttributes } = useLoaderData({ from: '/images/$id' });
  const [taggingValues, setTaggingValues] = React.useState<Record<string, string | string[]>>(() => {
    // 初始化标注值
    const initialValues: Record<string, string | string[]> = {};
    
    // 如果有已有标注，先填充
    if (image.tagging) {
      image.tagging.forEach(tag => {
        initialValues[tag.key] = tag.value;
      });
    }
    
    return initialValues;
  });
  
  // 构建属性树
  const attributeTree = React.useMemo(() => buildAttributeTree(taggingAttributes), [taggingAttributes]);
  
  // 跟踪展开的节点
  const [expanded, setExpanded] = React.useState<Record<string, boolean>>({});
  
  // 初始化时展开所有节点
  React.useEffect(() => {
    const expandedState: Record<string, boolean> = {};
    const expandNodes = (nodes: AttributeNode[]) => {
      nodes.forEach(node => {
        expandedState[node.path] = true;
        if (node.children?.length) {
          expandNodes(node.children);
        }
      });
    };
    expandNodes(attributeTree);
    setExpanded(expandedState);
  }, [attributeTree]);
  
  // 切换节点展开/折叠状态
  const toggleExpand = (path: string) => {
    setExpanded(prev => ({
      ...prev,
      [path]: !prev[path]
    }));
  };

  // 处理表单值变更
  const handleValueChange = (key: string, value: string | string[]) => {
    setTaggingValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('提交标注数据:', taggingValues);
    // 这里可以添加保存数据的逻辑
    alert('标注数据已保存！');
  };

  return (
    <div className="flex" style={{ height: 'calc(100vh - 120px)' }}>
      {/* 左侧固定区域 - 图片预览和基本信息 */}
      <div className="w-1/3 min-w-[400px] border-r border-border bg-card overflow-y-auto">
        <div className="p-6 space-y-6">
          {/* 页面标题和保存按钮 */}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold">图片 {image.id} 标注</h1>
            <Button onClick={handleSubmit} type="submit" size="sm">保存标注</Button>
          </div>

          {/* 图片预览 */}
          <div>
            <h2 className="text-xl font-semibold mb-4">图片预览</h2>
            <div className="border rounded-lg overflow-hidden bg-background">
              <img
                src={image.url}
                alt={`Image ${image.id}`}
                className="w-full h-auto max-h-[300px] object-contain"
              />
            </div>
          </div>

          {/* 基本信息 */}
          <div>
            <h2 className="text-xl font-semibold mb-4">基本信息</h2>
            <div className="bg-background rounded-lg border">
              <Table>
                <TableBody>
                  <TableRow>
                    <TableHead className="w-24">ID</TableHead>
                    <TableCell>{image.id}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableHead>URL</TableHead>
                    <TableCell className="break-all text-sm">{image.url}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableHead>创建时间</TableHead>
                    <TableCell className="text-sm">{new Date(image.created_at).toLocaleString()}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableHead>更新时间</TableHead>
                    <TableCell className="text-sm">{new Date(image.updated_at).toLocaleString()}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      </div>

      {/* 右侧标注数据区域 */}
      <div className="flex-1 bg-card overflow-y-auto">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="flex justify-between items-center sticky top-0 bg-background/95 backdrop-blur-sm z-10 py-4 border-b">
            <h2 className="text-xl font-semibold">标注数据</h2>
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  // 切换所有节点的展开/折叠状态
                  if (Object.keys(expanded).length > 0) {
                    setExpanded({});
                  } else {
                    const expandedState: Record<string, boolean> = {};
                    const expandNodes = (nodes: AttributeNode[]) => {
                      nodes.forEach(node => {
                        expandedState[node.path] = true;
                        if (node.children?.length) {
                          expandNodes(node.children);
                        }
                      });
                    };
                    expandNodes(attributeTree);
                    setExpanded(expandedState);
                  }
                }}
              >
                {Object.keys(expanded).length > 0 ? '全部折叠' : '全部展开'}
              </Button>
              <Button type="submit" size="sm">保存标注</Button>
            </div>
          </div>

          <div className="bg-background rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-1/3">属性</TableHead>
                  <TableHead>值</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {/* 递归渲染树形结构 */}
                {renderAttributeNodes(attributeTree, 0)}
              </TableBody>
            </Table>
          </div>
        </form>
      </div>
    </div>
  );
  
  // 递归渲染属性节点
  function renderAttributeNodes(nodes: AttributeNode[], depth: number) {
    return nodes.map(node => {
      const hasChildren = node.children && node.children.length > 0;
      const isExpanded = expanded[node.path];
      
      return (
        <React.Fragment key={node.path}>
          <TableRow>
            <TableCell>
              <div style={{ paddingLeft: `${depth * 1.5}rem` }} className="flex items-center">
                {hasChildren ? (
                  <button
                    type="button"
                    onClick={() => toggleExpand(node.path)}
                    className="mr-2 focus:outline-none"
                  >
                    {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                  </button>
                ) : (
                  <span className="w-6 mr-2"></span>
                )}
                <span className={node.disabled ? 'text-muted-foreground' : ''}>{node.displayName}</span>
              </div>
            </TableCell>
            <TableCell>
              {node.isLeaf && (
                <>
                  {node.enumerations.length > 0 ? (
                    <select
                      value={taggingValues[node.key] as string || ''}
                      onChange={(e) => handleValueChange(node.key, e.target.value)}
                      className="w-full p-2 border rounded-md bg-background"
                      disabled={node.disabled}
                    >
                      <option value="">-- 请选择 --</option>
                      {node.enumerations.map((option, index) => (
                        <option key={option} value={option}>
                          {node.displayEnumerations[index] || option}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      value={taggingValues[node.key] as string || ''}
                      onChange={(e) => handleValueChange(node.key, e.target.value)}
                      className={`w-full p-2 border rounded-md bg-background ${node.disabled ? 'opacity-60 cursor-not-allowed' : ''}`}
                      placeholder={`请输入${node.displayName}`}
                      disabled={node.disabled}
                    />
                  )}
                </>
              )}
            </TableCell>
          </TableRow>
          
          {/* 递归渲染子节点 */}
          {hasChildren && isExpanded && renderAttributeNodes(node.children!, depth + 1)}
        </React.Fragment>
      );
    });
  }
}
