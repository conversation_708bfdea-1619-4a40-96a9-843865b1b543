import { Button } from '@/components/ui/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table';
import { taggingAttributes, type TaggingAttribute } from '@/types';
import { createFileRoute } from '@tanstack/react-router';
import type { ColumnDef, ExpandedState } from '@tanstack/react-table';
import {
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, ChevronRight } from 'lucide-react';
import * as React from 'react';

// Define a new type for the hierarchical data
interface AttributeNode extends TaggingAttribute {
  children?: AttributeNode[];
}

// Helper function to build the tree structure
function buildTree(attributes: TaggingAttribute[]): AttributeNode[] {
  const tree: AttributeNode[] = [];
  const map: { [key: string]: AttributeNode } = {};

  attributes.forEach(attr => {
    map[attr.key] = { ...attr, children: [] };
  });

  attributes.forEach(attr => {
    const parentKey = attr.key.substring(0, attr.key.lastIndexOf('.'));
    if (parentKey && map[parentKey]) {
      map[parentKey].children?.push(map[attr.key]);
    } else {
      tree.push(map[attr.key]);
    }
  });

  return tree;
}

const attributeTree = buildTree(taggingAttributes);

export const Route = createFileRoute('/attributes')({
  component: AttributesPage,
});

function AttributesPage() {
  const [data] = React.useState(() => [...attributeTree]);
  // Create an expanded state with all rows expanded by default
  const [expanded, setExpanded] = React.useState<ExpandedState>(() => {
    // Create an object with all rows expanded
    const expandedObj: Record<string, boolean> = {};
    const expandRow = (rows: AttributeNode[]) => {
      rows.forEach((row, index) => {
        expandedObj[index] = true;
        if (row.children) {
          expandRow(row.children);
        }
      });
    };
    expandRow(attributeTree);
    return expandedObj;
  });

  const columns = React.useMemo<ColumnDef<AttributeNode>[]>(
    () => [
      {
        accessorKey: 'displayName',
        header: '显示名称',
        cell: ({ row, getValue }) => (
          <div style={{ paddingLeft: `${row.depth * 2}rem` }}>
            {row.getCanExpand() ? (
              <button
                {...{
                  onClick: row.getToggleExpandedHandler(),
                  style: { cursor: 'pointer' },
                }}
              >
                {row.getIsExpanded() ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
              </button>
            ) : (
              ' '
            )}{' '}
            {getValue<string>()}
          </div>
        ),
      },
      {
        accessorKey: 'key',
        header: '键值',
      },
      {
        accessorKey: 'name',
        header: '名称',
      },
      {
        accessorKey: 'enumerations',
        header: '枚举值（英文）',
        cell: ({ getValue }) => {
          const enums = getValue<string[]>();
          return enums.join(', ');
        },
      },
      {
        accessorKey: 'displayEnumerations',
        header: '枚举值（中文）',
        cell: ({ getValue }) => {
          const enums = getValue<string[]>();
          return enums.join(', ');
        },
      },
    ],
    []
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      expanded,
    },
    onExpandedChange: setExpanded,
    getSubRows: row => row.children,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
  });

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">标注属性</h1>
      <div className="mb-4">
        <Button
          onClick={() => {
            // Toggle between all expanded and all collapsed
            if (Object.keys(expanded).length > 0) {
              setExpanded({});
            } else {
              const expandedState: Record<string, boolean> = {};
              const expandRows = (rows: AttributeNode[], parentId?: string) => {
                rows.forEach((row, index) => {
                  const id = parentId ? `${parentId}.${index}` : `${index}`;
                  expandedState[id] = true;
                  if (row.children?.length) {
                    expandRows(row.children, id);
                  }
                });
              };
              expandRows(attributeTree);
              setExpanded(expandedState);
            }
          }}
          variant="secondary"
        >
          {Object.keys(expanded).length > 0 ? '全部折叠' : '全部展开'}
        </Button>
      </div>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map(row => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map(cell => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
    </div>
  );
}
