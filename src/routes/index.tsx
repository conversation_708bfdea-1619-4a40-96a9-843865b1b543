import ImageItem from '@/components/ImageItem';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/Pagination';
import { createFileRoute, useLoaderData } from '@tanstack/react-router';

type ImageSearch = {
  page: number;
  pageSize: number;
}

export const Route = createFileRoute('/')({
  validateSearch: (search: Record<string, unknown>): ImageSearch => {
    return {
      page: Number(search?.page ?? 1),
      pageSize: Number(search?.pageSize ?? 10),
    };
  },
  loaderDeps: ({ search }) => ({
    page: search.page,
    pageSize: search.pageSize,
  }),
  loader: async ({ deps: { page, pageSize } }) => {
    try {
      // 动态导入数据库连接，确保只在服务器端运行
      const { db } = await import('@/database');

      // 查询总数量
      const totalCountResult = await db
        .selectFrom('tagging_image')
        .select((eb) => eb.fn.count('id').as('count'))
        .where('deleted_at', 'is', null)
        .executeTakeFirstOrThrow();

      const totalImages = Number(totalCountResult.count);
      const totalPages = Math.ceil(totalImages / pageSize);

      // 查询分页数据
      const images = await db
        .selectFrom('tagging_image')
        .selectAll()
        .where('deleted_at', 'is', null)
        .orderBy('created_at', 'desc')
        .limit(pageSize)
        .offset((page - 1) * pageSize)
        .execute();

      return { images, page, pageSize, totalPages, totalImages };
    } catch (error) {
      console.error('Failed to load images:', error);
      // 返回空数据作为fallback
      return { images: [], page, pageSize, totalPages: 0, totalImages: 0 };
    }
  },
  component: Index,
})

function Index() {
  const { images, page, pageSize, totalPages } = useLoaderData({ from: '/' });

  return (
    <div className="container mx-auto p-8 space-y-8">
        <section>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {images.map((image) => (
              <ImageItem image={image} key={image.id} />
            ))}
          </div>
        </section>

        <section>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  to={Route.path}
                  search={{
                    page: Math.max(1, page - 1),
                    pageSize,
                  }} 
                />
              </PaginationItem>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((p) => (
                <PaginationItem key={p}>
                  <PaginationLink 
                    to={Route.path}
                    search={{
                      page: p,
                      pageSize,
                    }}   
                    isActive={p === page}
                  >
                    {p}
                  </PaginationLink>
                </PaginationItem>
              ))}
              <PaginationItem>
                <PaginationNext 
                  to={Route.path}
                  search={{
                    page: Math.min(totalPages, page + 1),
                    pageSize,
                  }}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </section>
    </div>
  )
}
