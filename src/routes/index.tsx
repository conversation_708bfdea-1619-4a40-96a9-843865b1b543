import ImageItem from '@/components/ImageItem';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/Pagination';
import { type TaggingImage } from '@/types';
import { createFileRoute, useLoaderData } from '@tanstack/react-router';

type ImageSearch = {
  page: number;
  pageSize: number;
}

export const Route = createFileRoute('/')({
  validateSearch: (search: Record<string, unknown>): ImageSearch => {
    return {
      page: Number(search?.page ?? 1),
      pageSize: Number(search?.pageSize ?? 10),
    };
  },
  loaderDeps: ({ search }) => ({
    page: search.page,
    pageSize: search.pageSize,
  }),
  loader: async ({ deps: { page, pageSize } }) => {
    const totalImages = 100; // 模拟总图片数量
    const totalPages = Math.ceil(totalImages / pageSize);

    // 模拟图片数据
    const images: TaggingImage[] = Array.from({ length: pageSize }, (_, i) => {
      const id = (page - 1) * pageSize + i + 1;
      return {
        id,
        url: `https://picsum.photos/seed/${id}/400/300`, // 占位图
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      };
    });

    return { images, page, pageSize, totalPages, totalImages };
  },
  component: Index,
})

function Index() {
  const { images, page, pageSize, totalPages } = useLoaderData({ from: '/' });

  return (
    <div className="container mx-auto p-8 space-y-8">
        <section>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {images.map((image) => (
              <ImageItem image={image} key={image.id} />
            ))}
          </div>
        </section>

        <section>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  to={Route.path}
                  search={{
                    page: Math.max(1, page - 1),
                    pageSize,
                  }} 
                />
              </PaginationItem>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((p) => (
                <PaginationItem key={p}>
                  <PaginationLink 
                    to={Route.path}
                    search={{
                      page: p,
                      pageSize,
                    }}   
                    isActive={p === page}
                  >
                    {p}
                  </PaginationLink>
                </PaginationItem>
              ))}
              <PaginationItem>
                <PaginationNext 
                  to={Route.path}
                  search={{
                    page: Math.min(totalPages, page + 1),
                    pageSize,
                  }}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </section>
    </div>
  )
}
