import { Button } from '@/components/ui/Button';
import { ImagePreview } from '@/components/ui/ImagePreview';
import { type TaggingImage } from '@/types';
import { Link } from '@tanstack/react-router';
import * as React from 'react';

interface ImageItemProps {
  image: TaggingImage;
}

const ImageItem: React.FC<ImageItemProps> = ({ image }) => {
  return (
    <div key={image.id} className="border rounded-lg p-4 shadow-sm">
      <ImagePreview
        src={image.url}
        alt={`Image ${image.id}`}
        triggerClassName="w-full h-48 mb-4 cursor-pointer"
        contentClassName="max-w-screen-lg max-h-screen"
      />
      <div className="text-sm space-y-1">
        <p><strong>ID:</strong> {image.id}</p>
        <p><strong>URL:</strong> {image.url}</p>
        <p><strong>Created:</strong> {new Date(image.created_at).toLocaleString()}</p>
        <p><strong>Updated:</strong> {new Date(image.updated_at).toLocaleString()}</p>
        <p><strong>Deleted:</strong> {image.deleted_at ? new Date(image.deleted_at).toLocaleString() : 'N/A'}</p>
        <div className="mt-4">
          <Link to="/images/$id" params={{ id: String(image.id) }}>
            <Button variant="secondary" className="w-full">编辑标注</Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ImageItem;
