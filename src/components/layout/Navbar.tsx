import { Button } from '@/components/ui/Button';
import { Link } from '@tanstack/react-router';
import * as React from 'react';
import { useEffect, useState } from 'react';

const Navbar: React.FC = () => {
  const [isDark, setIsDark] = useState(false);

  // 切换主题并保存到 localStorage
  const toggleTheme = () => {
    const newDarkMode = !isDark;
    setIsDark(newDarkMode);
    
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  useEffect(() => {
    // 从 localStorage 读取主题设置
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    // 如果有保存的主题设置，使用它；否则，使用系统偏好
    const shouldUseDark = savedTheme === 'dark' || (savedTheme === null && prefersDark);
    
    if (shouldUseDark) {
      document.documentElement.classList.add('dark');
      setIsDark(true);
    } else {
      document.documentElement.classList.remove('dark');
      setIsDark(false);
    }
  }, []);

  return (
    <div className="flex justify-between items-center p-8 bg-card text-card-foreground shadow-md">
      <div className="flex items-center space-x-4">
        <h1 className="text-2xl font-bold">数据打标平台</h1>
        <Link to="/" search={{ page: 1, pageSize: 10 }} className="hover:text-gray-300">图片列表</Link>
        <Link to="/attributes" className="hover:text-gray-300">打标属性</Link>
      </div>
      <Button 
        onClick={toggleTheme}
        variant="outline"
      >
        {isDark ? '🌞 Light' : '🌙 Dark'}
      </Button>
    </div>
  );
};

export default Navbar;
