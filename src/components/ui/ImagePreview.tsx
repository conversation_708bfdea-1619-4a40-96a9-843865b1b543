import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/Dialog"
import * as AspectRatioPrimitive from "@radix-ui/react-aspect-ratio"
import * as React from "react"

interface ImagePreviewProps {
  src: string
  alt: string
  ratio?: number
  triggerClassName?: string
  contentClassName?: string
}

const ImagePreview: React.FC<ImagePreviewProps> = ({
  src,
  alt,
  ratio = 16 / 9,
  triggerClassName,
  contentClassName,
}) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className={triggerClassName}>
          <AspectRatioPrimitive.Root ratio={ratio}>
            <img src={src} alt={alt} className="h-full w-full rounded-md object-cover" />
          </AspectRatioPrimitive.Root>
        </div>
      </DialogTrigger>
      <DialogContent className={contentClassName}>
        <img src={src} alt={alt} className="h-auto w-full" />
      </DialogContent>
    </Dialog>
  )
}

export { ImagePreview }
