import { type ColumnType, type Generated, type Insertable, type Selectable, type Updateable } from 'kysely';

export interface TaggingAttribute {
  key: string;
  name: string;
  displayName: string;
  enumerations: string[];
  displayEnumerations: string[];
}

// 标注值表定义
export interface TaggingValueTable {
  id: Generated<number>;
  image_id: number;
  key: string;
  value: string;
  created_at: ColumnType<Date, string | undefined, never>;
  updated_at: ColumnType<Date, string | Date, string | Date>;
}

// 图片表定义
export interface TaggingImageTable {
  id: Generated<number>;
  url: string;
  created_at: ColumnType<Date, string | undefined, never>;
  updated_at: ColumnType<Date, string | Date, string | Date>;
  deleted_at: ColumnType<Date | null, string | Date | null, string | Date | null>;
}

// 定义查询结果类型
export type TaggingValue = Selectable<TaggingValueTable>;
export type NewTaggingValue = Insertable<TaggingValueTable>;
export type TaggingValueUpdate = Updateable<TaggingValueTable>;

export type TaggingImage = Selectable<TaggingImageTable>;
export type NewTaggingImage = Insertable<TaggingImageTable>;
export type TaggingImageUpdate = Updateable<TaggingImageTable>;

// 数据库表定义
export interface Database {
  tagging_value: TaggingValueTable;
  tagging_image: TaggingImageTable;
}

export const taggingAttributes: TaggingAttribute[] = [
  { key: 'image_path', name: 'image_path', displayName: '图片路径', enumerations: [], displayEnumerations: [] },
  { key: 'analysis', name: 'analysis', displayName: '分析', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Model', name: 'Model', displayName: '模特', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Model.Region', name: 'Region', displayName: '地域', enumerations: ["Nordic", "Greek", "Italian", "Spanish", "French", "German", "Dutch", "British", "Russian", "American", "South American", "Chinese", "Japanese", "Korean", "Southeast Asian", "Indian", "Egyptian", "Sub-Saharan African", "Maasai", "Caribbean (Jamaican)", "Māori", "Romani"], displayEnumerations: ["北欧", "希腊", "意大利", "西班牙", "法国", "德国", "荷兰", "英国", "俄罗斯", "美国", "南美", "中国", "日本", "韩国", "东南亚", "印度", "埃及", "撒哈拉以南非洲", "马赛", "加勒比(牙买加)", "毛利", "罗姆"] },
  { key: 'analysis.Model.Gender', name: 'Gender', displayName: '性别', enumerations: ["Male", "Female"], displayEnumerations: ["男性", "女性"] },
  { key: 'analysis.Model.Age', name: 'Age', displayName: '年龄', enumerations: ["0-3", "3-5", "5-9", "10-14", "15-25", "26-35", "36-45", "46-60", "60+"], displayEnumerations: ["0-3岁", "3-5岁", "5-9岁", "10-14岁", "15-25岁", "26-35岁", "36-45岁", "46-60岁", "60岁以上"] },
  { key: 'analysis.Model.Hairstyle', name: 'Hairstyle', displayName: '发型', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Model.Hairstyle.Length', name: 'Length', displayName: '长度', enumerations: ["Bald", "Pixie cut", "Short hair", "Ear-length bob", "Shoulder-length", "Collarbone-length midi", "Shoulder-length long hair", "Waist-length long hair", "Very long hair"], displayEnumerations: ["光头", "精灵短发", "短发", "及耳波波头", "及肩发", "锁骨发", "及肩长发", "及腰长发", "超长发"] },
  { key: 'analysis.Model.Hairstyle.Curl Pattern', name: 'Curl Pattern', displayName: '卷曲度', enumerations: ["Straight", "Loose waves", "Tight curls", "Wavy"], displayEnumerations: ["直发", "松散波浪", "紧密卷曲", "波浪"] },
  { key: 'analysis.Model.Hairstyle.Color', name: 'Color', displayName: '颜色', enumerations: ["Natural black", "Dark brown", "Light brown", "Blonde", "Auburn", "Grey", "Bleached color", "Matte", "Shiny"], displayEnumerations: ["自然黑", "深棕", "浅棕", "金色", "赤褐色", "灰色", "漂染", "哑光", "亮光"] },
  { key: 'analysis.Model.Hairstyle.Volume', name: 'Volume', displayName: '发量', enumerations: ["Low volume", "Medium volume", "High volume"], displayEnumerations: ["发量少", "发量中等", "发量多"] },
  { key: 'analysis.Model.Hairstyle.Bangs', name: 'Bangs', displayName: '刘海', enumerations: ["None", "Straight-across bangs", "Airy bangs", "Curtain bangs", "French-girl fringe", "Hime cut", "Side-swept bangs", "Straight-Across bangs", "Curved bangs", "Sheer bangs", "Chunky bangs"], displayEnumerations: ["无刘海", "齐刘海", "空气刘海", "窗帘刘海", "法式刘海", "公主切", "侧分刘海", "一刀切刘海", "弧形刘海", "薄刘海", "厚刘海"] },
  { key: 'analysis.Model.Hairstyle.Style', name: 'Style', displayName: '风格', enumerations: ["Loose", "Mullet", "Xin Zhilei hairstyle", "Layered bob", "Sleek back short hair", "Chinese Republican-era hand-pushed waves", "Precise bob", "Mohawk", "Bowl cut", "Airy bangs with long straight hair", "Long layered straight hair", "Big waves", "Woolly curls", "Textured perm", "Undercut", "American crew cut", "Side part", "Buzz cut", "Crew cut", "Slicked back", "Medium-length slicked back", "Layered short hair", "Ponytail (Sleek high", "Voluminous high", "Very high", "Low", "Side)", "Braids (Three-strand", "Fishtail", "Dreadlocks", "Waterfall", "Boxer braids", "Cleopatra updo)", "Updo (Low chignon", "Double buns", "Half-up bun", "Flower bun", "Korean messy updo", "Hepburn updo", "Pompadour", "Qing dynasty Liangbatou", "Greek goddess knot", "Japanese Himegami", "Japanese samurai hairstyle)"], displayEnumerations: ["披发", "鲻鱼头", "辛芷蕾发型", "层次感波波头", "背头短发", "民国手推波", "精准波波头", "莫霍克", "锅盖头", "空气刘海长直发", "长直碎发", "大波浪", "羊毛卷", "纹理烫", "底切", "美式圆寸", "侧分", "寸头", "平头", "油头", "中长油头", "分层短发", "马尾(高马尾", "高颅顶马尾", "超高马尾", "低马尾", "侧马尾)", "辫子(三股辫", "鱼骨辫", "脏辫", "瀑布辫", "拳击辫", "埃及艳后发髻)", "发髻(低发髻", "双丸子头", "半扎丸子头", "花苞头", "韩式乱发髻", "赫本头", "飞机头", "清代两把头", "希腊女神结", "日式姬发", "日式武士头)"] },
  { key: 'analysis.Model.Makeup', name: 'Makeup', displayName: '妆容', enumerations: ["Glass skin makeup", "Big-eyed youthful makeup", "Black cat makeup", "Tomie makeup", "Asian baddie makeup", "Clean no-makeup makeup", "Spring warm tones", "Winter cool tones", "Apathetic tired makeup", "Glamorous shimmer makeup", "Italian passion makeup", "Gothic emotional makeup", "Smudged smoky makeup", "Bayonetta makeup", "Y2K makeup", "Cold AI girl makeup", "E-girl makeup", "Artistic makeup", "Soft messy makeup", "Balletcore makeup", "Coquette makeup", "Fox/Snake eye makeup", "English girl", "Latina", "African", "Danish", "Arabic", "Korean-style", "Thai"], displayEnumerations: ["水光肌", "大眼幼态妆", "黑猫妆", "富江妆", "亚洲辣妹妆", "清透裸妆", "春日暖阳", "冬日冷调", "厌世疲惫妆", "闪耀星辰", "意式热情", "哥特情绪", "迷离烟熏", "贝优妮塔", "Y2K", "冷感AI", "E-girl", "艺术妆", "柔和乱妆", "芭蕾风", "妖艳风", "狐狸/蛇眼妆", "英伦女孩", "拉丁裔", "非洲裔", "丹麦", "阿拉伯", "韩式", "泰式"] },
  { key: 'analysis.Model.Gaze Expression', name: 'Gaze Expression', displayName: '眼神', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Model.Physique', name: 'Physique', displayName: '体型', enumerations: ["Tall", "Medium height", "Petite", "Slender", "Voluptuous", "Even", "Muscular", "Slightly plump", "Obese", "Childlike", "Pregnant", "Elderly posture"], displayEnumerations: ["高挑", "中等身高", "娇小", "纤细", "丰满", "匀称", "肌肉型", "微胖", "肥胖", "儿童身材", "怀孕", "老年姿态"] },
  { key: 'analysis.Model.Skin Tone', name: 'Skin Tone', displayName: '肤色', enumerations: ["undertone (Warm: Yellow/Golden; Cool: Pink/Blue; Neutral)", "depth (Porcelain", "Fair", "Medium", "Tan", "Deep", "Dark", "Olive)", "evenness (Even or uneven)"], displayEnumerations: ["底色(暖色:黄/金;冷色:粉/蓝;中性)", "深度(瓷白", "白皙", "中等", "古铜", "深色", "黝黑", "橄榄)", "均匀度(均匀或不均匀)"] },
  { key: 'analysis.Model.Posture', name: 'Posture', displayName: '姿态', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Model.Head Position', name: 'Head Position', displayName: '头部位置', enumerations: ["Facing forward", "Head down", "Head up", "Head tilted", "Head thrown back", "Head buried", "Head in hands", "Looking sideways", "Head down looking sideways", "Head up looking sideways", "Chin raised", "Leaning forward", "Leaning sideways", "Head back mouth open", "Head down glancing sideways", "Face buried in shoulder (side)", "Side-lean looking back", "Bent forward head lowered", "Mid-hair flip", "Chin resting looking sideways", "Hat pulled low"], displayEnumerations: ["正脸", "低头", "抬头", "歪头", "仰头", "埋头", "手捧头", "侧脸", "低头侧脸", "抬头侧脸", "抬下巴", "前倾", "侧倾", "仰头张嘴", "低头侧瞟", "埋肩(侧)", "侧身回望", "前屈低头", "甩发瞬间", "托腮侧望", "压低帽檐"] },
  { key: 'analysis.Model.Model Type', name: 'Model Type', displayName: '模特类型', enumerations: ["Sickly chic", "Intellectual", "Student", "Girlish", "Romantic vintage", "Gothic", "Punk", "Workplace", "Noble", "Sexy", "Sporty", "Sweet but cool", "Athletic", "Artistically distinctive model", "Futuristic tech", "Zen style", "Minimalist androgynous"], displayEnumerations: ["病态美", "知识分子", "学生气", "少女感", "浪漫复古", "哥特", "朋克", "职场", "贵族", "性感", "运动", "甜酷", "健美", "艺术感独特模特", "未来科技", "禅意", "极简中性"] },
  { key: 'analysis.Clothing', name: 'Clothing', displayName: '服装', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Top', name: 'Top', displayName: '上装', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Top.Style', name: 'Style', displayName: '风格', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Top.Length', name: 'Length', displayName: '长度', enumerations: ["Above-waist-length", "waist-length", "upper-hip length", "Low-hip length", "Mid-thigh", "knee-length", "Calf-length", "ankle-length", "full-length", "Breaking slightly", "Floor-length", "Puddle hem"], displayEnumerations: ["露脐", "及腰", "及臀", "臀下", "大腿中", "及膝", "及踝", "及地", "微拖地", "拖地", "堆积"] },
  { key: 'analysis.Clothing.Top.Fit', name: 'Fit', displayName: '版型', enumerations: ["Skin-tight", "Slim fit", "Oversized", "Super oversized"], displayEnumerations: ["紧身", "修身", "宽松", "超宽松"] },
  { key: 'analysis.Clothing.Top.Sleeve Length', name: 'Sleeve Length', displayName: '袖长', enumerations: ["Sleeveless", "Cap sleeve", "Short sleeve", "Half sleeve", "Three-quarter sleeve", "Long sleeve", "Extra-long sleeve"], displayEnumerations: ["无袖", "盖袖", "短袖", "半袖", "七分袖", "长袖", "超长袖"] },
  { key: 'analysis.Clothing.Top.Shoulder Design', name: 'Shoulder Design', displayName: '肩部设计', enumerations: ["Standard shoulder line", "Drop shoulder", "Raglan sleeve", "Kimono sleeve", "Yoke shoulder", "No shoulder seam design"], displayEnumerations: ["正肩", "落肩", "插肩袖", "和服袖", "育克肩", "无肩线设计"] },
  { key: 'analysis.Clothing.Top.Hemline', name: 'Hemline', displayName: '下摆', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Top.Neckline', name: 'Neckline', displayName: '领口', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Top.Sleeve Type', name: 'Sleeve Type', displayName: '袖型', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Top.Pockets', name: 'Pockets', displayName: '口袋', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Top.Embellishments', name: 'Embellishments', displayName: '装饰', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Top.Material', name: 'Material', displayName: '材质', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Top.Color', name: 'Color', displayName: '颜色', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Top.Pattern', name: 'Pattern', displayName: '图案', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Top.Garment Feature', name: 'Garment Feature', displayName: '服装特征', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Bottom', name: 'Bottom', displayName: '下装', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Bottom.Style', name: 'Style', displayName: '风格', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Bottom.Waistline', name: 'Waistline', displayName: '腰线', enumerations: ["High waist", "Mid-rise", "Low waist", "Ultra-low waist"], displayEnumerations: ["高腰", "中腰", "低腰", "超低腰"] },
  { key: 'analysis.Clothing.Bottom.Length', name: 'Length', displayName: '长度', enumerations: ["Low-hip length", "Mid-thigh", "knee-length", "Calf-length", "ankle-length", "full-length", "Breaking slightly", "Floor-length", "Puddle hem"], displayEnumerations: ["臀下", "大腿中", "及膝", "及踝", "及地", "微拖地", "拖地", "堆积"] },
  { key: 'analysis.Clothing.Bottom.Hemline', name: 'Hemline', displayName: '下摆', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Bottom.Pockets', name: 'Pockets', displayName: '口袋', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Bottom.Embellishments', name: 'Embellishments', displayName: '装饰', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Bottom.Material', name: 'Material', displayName: '材质', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Bottom.Color', name: 'Color', displayName: '颜色', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Bottom.Pattern', name: 'Pattern', displayName: '图案', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Bottom.Garment Feature', name: 'Garment Feature', displayName: '服装特征', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Layering Piece', name: 'Layering Piece', displayName: '叠穿单品', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Shoes', name: 'Shoes', displayName: '鞋子', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Accessories', name: 'Accessories', displayName: '配饰', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Clothing.Other Significant Details', name: 'Other Significant Details', displayName: '其他重要细节', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Shooting Theme', name: 'Shooting Theme', displayName: '拍摄主题', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Shooting Theme.Intended Use', name: 'Intended Use', displayName: '用途', enumerations: ["Product lookbook", "Poster", "Portrait", "Runway shot", "Lifestyle photo", "Advertisement", "Magazine cover", "Social media content"], displayEnumerations: ["产品画册", "海报", "肖像", "T台照", "生活照", "广告", "杂志封面", "社交媒体内容"] },
  { key: 'analysis.Shooting Theme.Shooting Scene', name: 'Shooting Scene', displayName: '拍摄场景', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Shooting Theme.Post-Processing Style', name: 'Post-Processing Style', displayName: '后期风格', enumerations: ["Black & White", "High saturation strong contrast intense feel", "Film grain cinematic feel", "Light muted elegant feel", "Blurred impressionistic", "Soft matte texture", "Clean crisp clarity", "Low saturation grainy feel", "Light & shadow sculpting", "Color mood creation", "Surreal compositing", "Film simulation", "Ink painting/oil painting style", "Liquid metal distortion"], displayEnumerations: ["黑白", "高饱和强对比", "胶片颗粒电影感", "清淡优雅", "模糊印象派", "柔和哑光质感", "清晰明快", "低饱和颗粒感", "光影雕塑", "色彩情绪营造", "超现实合成", "胶片模拟", "水墨/油画风格", "液态金属扭曲"] },
  { key: 'analysis.Shooting Theme.Lighting', name: 'Lighting', displayName: '光线', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Shooting Theme.Lighting.Light Angle', name: 'Light Angle', displayName: '光线角度', enumerations: ["Frontal light", "Front-side light", "Side light", "Back-side light", "Direct backlight", "Side backlight", "Top light", "Bottom light", "Butterfly light", "Loop light", "Diffused light", "Contour light"], displayEnumerations: ["顺光", "前侧光", "侧光", "后侧光", "正逆光", "侧逆光", "顶光", "底光", "蝴蝶光", "环形光", "散射光", "轮廓光"] },
  { key: 'analysis.Shooting Theme.Lighting.Color Temperature', name: 'Color Temperature', displayName: '色温', enumerations: ["Cool Light", "Warm Light", "Neutral White Light"], displayEnumerations: ["冷光", "暖光", "中性白光"] },
  { key: 'analysis.Shooting Theme.Lighting.Exposure Level', name: 'Exposure Level', displayName: '曝光', enumerations: ["High exposure (Bright)", "Normal exposure (Natural)", "Low exposure (Dim)"], displayEnumerations: ["高曝光(明亮)", "正常曝光(自然)", "低曝光(昏暗)"] },
  { key: 'analysis.Shooting Theme.Lighting.Atmosphere', name: 'Atmosphere', displayName: '氛围', enumerations: ["Flat light", "Hard light", "Soft light", "Fluorescent light", "Natural light", "Dimensional light", "Mixed lighting"], displayEnumerations: ["平光", "硬光", "软光", "荧光", "自然光", "立体光", "混合光"] },
  { key: 'analysis.Shooting Theme.Watermark Analysis', name: 'Watermark Analysis', displayName: '水印分析', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Shooting Theme.Watermark Analysis.Existence', name: 'Existence', displayName: '有无', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Shooting Theme.Watermark Analysis.Specific Content', name: 'Specific Content', displayName: '具体内容', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Lens Language', name: 'Lens Language', displayName: '镜头语言', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Lens Language.Composition', name: 'Composition', displayName: '构图', enumerations: ["Full body", "Half body", "Mid-thigh", "Bust up", "Long shot", "Calf-length"], displayEnumerations: ["全身", "半身", "大腿", "胸像", "远景", "小腿"] },
  { key: 'analysis.Lens Language.Focal Length Effect', name: 'Focal Length Effect', displayName: '焦段', enumerations: ["Standard lens", "Medium telephoto", "Telephoto"], displayEnumerations: ["标准镜头", "中长焦", "长焦"] },
  { key: 'analysis.Lens Language.Perspective', name: 'Perspective', displayName: '视角', enumerations: ["Low angle", "Eye-level", "High angle", "Frontal", "Slight angle", "Profile", "Back:"], displayEnumerations: ["仰视", "平视", "俯视", "正面", "微侧", "侧面", "背面"] },
  { key: 'analysis.Style', name: 'Style', displayName: '风格', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Style.Primary Classification', name: 'Primary Classification', displayName: '一级分类', enumerations: ["Mature & Business", "Sunny & Youthful", "Sexy", "Retro & Cultural", "Cool & Edgy", "Minimalist & Sharp", "Relaxed & Effortless", "Artistic & Subcultural", "Resort & Vacation", "Dark & Avant-garde"], displayEnumerations: ["成熟商务", "阳光青春", "性感", "复古人文", "冷酷先锋", "极简锋利", "松弛随性", "艺术亚文化", "度假", "暗黑先锋"] },
  { key: 'analysis.Style.Secondary Classification', name: 'Secondary Classification', displayName: '二级分类', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Style.Secondary Classification.Mature & Business', name: 'Mature & Business', displayName: '成熟商务', enumerations: ["Old Money", "Business Formal", "Business Casual", "Business Street Style", "Korean Style", "Intellectual Style", "Maillardcore"], displayEnumerations: ["老钱风", "商务正装", "商务休闲", "商务街头", "韩式", "知识分子风", "美拉德"] },
  { key: 'analysis.Style.Secondary Classification.Sunny & Youthful', name: 'Sunny & Youthful', displayName: '阳光青春', enumerations: ["Boyish Charm", "Changgu Style", "Preppy Style", "Dopamine Style", "Country Style", "Jersey Style", "Retro Sporty"], displayEnumerations: ["少年感", "昌系", "预科生", "多巴胺", "乡村", "球衣", "复古运动"] },
  { key: 'analysis.Style.Secondary Classification.Sexy', name: 'Sexy', displayName: '性感', enumerations: ["Professional Sexy", "Henley Style", "DSQUARED2 Style", "Dark Sexy", "Sheer", "Hot Girl Style", "Barbiecore", "Lolita Style", "Hollywood Golden Age Glamour", "Classic Red Carpet", "Nightclub Style"], displayEnumerations: ["职业性感", "亨利衫", "DSQUARED2", "暗黑性感", "透视", "辣妹", "芭比风", "洛丽塔", "好莱坞黄金时代", "经典红毯", "夜店风"] },
  { key: 'analysis.Style.Secondary Classification.Retro & Cultural', name: 'Retro & Cultural', displayName: '复古人文', enumerations: ["Traditional Chinese", "Boxy Fit", "New Vintage", "Y2K Retro", "American Vintage", "Japanese Vintage", "Amekaji", "Italian Vintage", "British Vintage", "Barn Style", "American Workwear", "Japanese Workwear", "Urban Outdoor/Techwear", "Yama Style", "Classic Qipao/Cheongsam", "Republican Era Boudoir Style", "European Court Style"], displayEnumerations: ["国潮", "Boxy Fit", "新复古", "Y2K复古", "美式复古", "日式复古", "阿美咔叽", "意式复古", "英式复古", "工装风", "美式工装", "日式工装", "都市户外/机能", "山系", "经典旗袍", "民国闺房", "欧式宫廷"] },
  { key: 'analysis.Style.Secondary Classification.Cool & Edgy', name: 'Cool & Edgy', displayName: '冷酷先锋', enumerations: ["Wasteland/Dystopian", "Futuristic", "Techwear", "Deconstructivism", "Dark Avant-garde", "British High Street", "American High Street", "Vibe"], displayEnumerations: ["废土/反乌托邦", "未来主义", "机能", "解构主义", "暗黑先锋", "英伦高街", "美式高街", "Vibe"] },
  { key: 'analysis.Style.Secondary Classification.Minimalist & Sharp', name: 'Minimalist & Sharp', displayName: '极简锋利', enumerations: ["Cleanfit", "Korean Style", "Comfort Minimalism", "GorpCore/Outdoor Minimalism"], displayEnumerations: ["Cleanfit", "韩式", "舒适极简", "GorpCore/户外极简"] },
  { key: 'analysis.Style.Secondary Classification.Relaxed & Effortless', name: 'Relaxed & Effortless', displayName: '松弛随性', enumerations: ["New Chinese Style", "Athflow", "Cozy Fit/Lazy Style", "French Romantic", "Chinese Scholar Style", "Japanese/Korean Fresh Style"], displayEnumerations: ["新中式", "Athflow", "舒适/慵懒", "法式浪漫", "中式书生", "日韩清新"] },
  { key: 'analysis.Style.Secondary Classification.Artistic & Subcultural', name: 'Artistic & Subcultural', displayName: '艺术亚文化', enumerations: ["Punk Style", "Grunge", "Rock Style", "Metal Style", "Gothic", "Hippie", "Yuppie", "American Streetwear", "Hip-Hop Style", "Op Art Style"], displayEnumerations: ["朋克", "垃圾摇滚", "摇滚", "金属", "哥特", "嬉皮", "雅痞", "美式街头", "嘻哈", "欧普艺术"] },
  { key: 'analysis.Style.Secondary Classification.Resort & Vacation', name: 'Resort & Vacation', displayName: '度假', enumerations: ["Boho", "French Riviera", "French Countryside", "Island Resort", "Italian Dolce Vita", "Mori Girl"], displayEnumerations: ["波西米亚", "法式里维埃拉", "法式田园", "海岛度假", "意式甜蜜生活", "森系"] },
  { key: 'analysis.Style.Secondary Classification.Dark & Avant-garde', name: 'Dark & Avant-garde', displayName: '暗黑先锋', enumerations: ["Yohji Yamamoto Style", "Rick Owens Style", "Ann Demeulemeester Style", "JUUN.J Style"], displayEnumerations: ["山本耀司", "里克·欧文斯", "安·迪穆拉米斯特", "JUUN.J"] },
  { key: 'analysis.Style.Core Features', name: 'Core Features', displayName: '核心特征', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Style.Core Features.Color System', name: 'Color System', displayName: '色彩体系', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Style.Core Features.Cut & Silhouette', name: 'Cut & Silhouette', displayName: '剪裁与轮廓', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Style.Core Features.Material & Texture', name: 'Material & Texture', displayName: '材质与纹理', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Style.Core Features.Scene Atmosphere', name: 'Scene Atmosphere', displayName: '场景氛围', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Style.Core Features.Signature Elements', name: 'Signature Elements', displayName: '标志性元素', enumerations: [], displayEnumerations: [] },
  { key: 'analysis.Style.Overall Image Description', name: 'Overall Image Description', displayName: '图片总览', enumerations: [], displayEnumerations: [] },
  { key: 'status', name: 'status', displayName: '状态', enumerations: [], displayEnumerations: [] },
  { key: 'timestamp', name: 'timestamp', displayName: '时间戳', enumerations: [], displayEnumerations: [] },
  { key: 'summarize', name: 'summarize', displayName: '总结', enumerations: [], displayEnumerations: [] },
  { key: 'total_processing_time', name: 'total_processing_time', displayName: '总处理时间', enumerations: [], displayEnumerations: [] },
];
