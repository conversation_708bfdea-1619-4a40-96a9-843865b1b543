/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as AttributesRouteImport } from './routes/attributes'
import { Route as IndexRouteImport } from './routes/index'
import { Route as ImagesIdRouteImport } from './routes/images.$id'

const AttributesRoute = AttributesRouteImport.update({
  id: '/attributes',
  path: '/attributes',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const ImagesIdRoute = ImagesIdRouteImport.update({
  id: '/images/$id',
  path: '/images/$id',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/attributes': typeof AttributesRoute
  '/images/$id': typeof ImagesIdRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/attributes': typeof AttributesRoute
  '/images/$id': typeof ImagesIdRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/attributes': typeof AttributesRoute
  '/images/$id': typeof ImagesIdRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/attributes' | '/images/$id'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/attributes' | '/images/$id'
  id: '__root__' | '/' | '/attributes' | '/images/$id'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AttributesRoute: typeof AttributesRoute
  ImagesIdRoute: typeof ImagesIdRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/attributes': {
      id: '/attributes'
      path: '/attributes'
      fullPath: '/attributes'
      preLoaderRoute: typeof AttributesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/images/$id': {
      id: '/images/$id'
      path: '/images/$id'
      fullPath: '/images/$id'
      preLoaderRoute: typeof ImagesIdRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AttributesRoute: AttributesRoute,
  ImagesIdRoute: ImagesIdRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
