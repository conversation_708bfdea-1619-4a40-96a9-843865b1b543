import dotenv from 'dotenv';
import { <PERSON><PERSON><PERSON>, MysqlDialect } from 'kysely';
import { createPool } from 'mysql2'; // do not use 'mysql2/promises'!
import type { Database } from './types'; // this is the Database interface we defined earlier

// If NODE_ENV is set, load the environment-specific file first.
if (process.env.NODE_ENV) {
  dotenv.config({ path: `.env.${process.env.NODE_ENV}` });
}

// Then, load the base .env file, overriding any existing variables.
dotenv.config({ override: true });

const dialect = new MysqlDialect({
  pool: createPool({
    database: process.env.DB_DATABASE,
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: Number(process.env.DB_PORT ?? 3306),
    connectionLimit: Number(process.env.DB_CONNECTION_LIMIT ?? 10),
  })
})

// Database interface is passed to <PERSON><PERSON><PERSON>'s constructor, and from now on, <PERSON><PERSON><PERSON> 
// knows your database structure.
// Dialect is passed to <PERSON><PERSON><PERSON>'s constructor, and from now on, <PERSON><PERSON><PERSON> knows how 
// to communicate with your database.
export const db = new Kysely<Database>({
  dialect,
})
