import { Kysely, sql } from 'kysely'

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema
    .createTable('tagging_image')
    .addColumn('id', 'integer', (col) => col.primaryKey().autoIncrement())
    .addColumn('url', 'text', (col) => col.notNull())
    .addColumn('created_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('updated_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('deleted_at', 'timestamp')
    .execute()

  await db.schema
    .createTable('tagging_value')
    .addColumn('id', 'integer', (col) => col.primaryKey().autoIncrement())
    .addColumn('image_id', 'integer', (col) =>
      col.references('tagging_image.id').notNull()
    )
    .addColumn('key', 'varchar(255)', (col) => col.notNull())
    .addColumn('value', 'varchar(255)', (col) => col.notNull())
    .addColumn('created_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('updated_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .execute()
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable('tagging_value').execute()
  await db.schema.dropTable('tagging_image').execute()
}
