import { Kysely, sql } from 'kysely'
import { executeWithLog } from '../lib/migration-helpers.js'

export async function up(db: Kysely<any>): Promise<void> {
  await executeWithLog(
    'Creating tagging_image table',
    db.schema
      .createTable('tagging_image')
      .addColumn('id', 'integer', (col) => col.primaryKey().autoIncrement())
      .addColumn('url', 'text', (col) => col.notNull())
      .addColumn('created_at', 'timestamp', (col) =>
        col.defaultTo(sql`now()`).notNull()
      )
      .addColumn('updated_at', 'timestamp', (col) =>
        col.defaultTo(sql`now()`).notNull()
      )
      .addColumn('deleted_at', 'timestamp')
  )

  await executeWithLog(
    'Creating tagging_value table',
    db.schema
      .createTable('tagging_value')
      .addColumn('id', 'integer', (col) => col.primaryKey().autoIncrement())
      .addColumn('image_id', 'integer', (col) =>
        col.references('tagging_image.id').notNull()
      )
      .addColumn('key', 'varchar(255)', (col) => col.notNull())
      .addColumn('value', 'varchar(255)', (col) => col.notNull())
      .addColumn('created_at', 'timestamp', (col) =>
        col.defaultTo(sql`now()`).notNull()
      )
      .addColumn('updated_at', 'timestamp', (col) =>
        col.defaultTo(sql`now()`).notNull()
      )
  )
}

export async function down(db: Kysely<any>): Promise<void> {
  await executeWithLog(
    'Dropping tagging_value table',
    db.schema.dropTable('tagging_value')
  )

  await executeWithLog(
    'Dropping tagging_image table',
    db.schema.dropTable('tagging_image')
  )
}
