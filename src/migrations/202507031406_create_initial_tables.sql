create table `tagging_image` (
  `id` integer primary key auto_increment,
  `url` text not null,
  `created_at` datetime default now() not null,
  `updated_at` datetime default now() not null,
  `deleted_at` datetime
);
create table `tagging_value` (
  `id` integer primary key auto_increment,
  `image_id` integer not null references `tagging_image` (`id`),
  `key` varchar(255) not null,
  `value` varchar(255) not null,
  `created_at` datetime default now() not null,
  `updated_at` datetime default now() not null
);