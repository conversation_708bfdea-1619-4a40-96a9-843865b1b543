/**
 * 迁移辅助工具
 * 提供在开发环境下输出 SQL 语句的功能
 */

import type { CompiledQuery } from 'kysely'

/**
 * 在开发环境下输出 SQL 语句到控制台
 * @param description - SQL 操作的描述
 * @param query - 可编译的查询对象
 */
export function logSQLInDev(description: string, query: { compile(): CompiledQuery }) {
  if (process.env.NODE_ENV === 'development' || !process.env.NODE_ENV) {
    const compiled = query.compile()
    console.log(`${description}:`)
    console.log(compiled.sql)
    if (compiled.parameters.length > 0) {
      console.log('Parameters:', compiled.parameters)
    }
    console.log('---')
  }
}

/**
 * 执行查询并在开发环境下输出 SQL
 * @param description - SQL 操作的描述
 * @param query - 可执行的查询对象
 */
export async function executeWithLog<T>(
  description: string, 
  query: { compile(): CompiledQuery; execute(): Promise<T> }
): Promise<T> {
  logSQLInDev(description, query)
  return await query.execute()
}
