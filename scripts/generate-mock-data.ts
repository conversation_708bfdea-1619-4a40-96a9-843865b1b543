#!/usr/bin/env tsx

/**
 * 生成覆盖所有 taggingAttributes 字段的模拟数据
 * 使用 Faker.js 生成真实的模拟数据
 */

import { faker } from '@faker-js/faker';
import { promises as fs } from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从types文件导入完整的taggingAttributes
import { taggingAttributes } from '../src/types/index.js';

interface MockImage {
  id: number
  url: string
  created_at: string
  updated_at: string
  attributes: Record<string, string>
}

/**
 * 从枚举值中随机选择一个值
 */
function getRandomEnumValue(enumerations: string[]): string {
  if (enumerations.length === 0) return ''
  return faker.helpers.arrayElement(enumerations)
}

/**
 * 根据属性key生成合适的mock值
 */
function generateMockValue(attribute: any): string {
  // 如果有枚举值，优先使用枚举值
  if (attribute.enumerations && attribute.enumerations.length > 0) {
    return getRandomEnumValue(attribute.enumerations)
  }

  // 根据key的类型生成相应的mock数据
  const key = attribute.key.toLowerCase()

  if (key.includes('color') || key.includes('颜色')) {
    return faker.helpers.arrayElement(['红色', '蓝色', '绿色', '黄色', '黑色', '白色', '灰色', '粉色'])
  }

  if (key.includes('material') || key.includes('材质')) {
    return faker.helpers.arrayElement(['棉', '丝绸', '羊毛', '聚酯纤维', '牛仔布', '皮革', '雪纺', '针织'])
  }

  if (key.includes('pattern') || key.includes('图案')) {
    return faker.helpers.arrayElement(['纯色', '条纹', '格子', '波点', '花卉', '几何', '动物纹', '抽象'])
  }

  if (key.includes('time') || key.includes('timestamp')) {
    return new Date().toISOString()
  }

  if (key.includes('url') || key.includes('path')) {
    return 'https://example.com/image.jpg'
  }

  if (key.includes('description') || key.includes('analysis') || key.includes('总览')) {
    return '这是一个详细的分析描述'
  }

  if (key.includes('status') || key.includes('状态')) {
    return faker.helpers.arrayElement(['pending', 'completed', 'in_progress', 'failed'])
  }

  if (key.includes('processing_time') || key.includes('处理时间')) {
    return faker.number.int({ min: 100, max: 5000 }).toString() + 'ms'
  }

  if (key.includes('summarize') || key.includes('总结')) {
    return '这是一个总结性的描述文本'
  }

  // 默认返回相关的描述性文本
  return '默认描述文本'
}

/**
 * 生成单张图片的完整mock数据
 */
function generateMockImage(id: number): MockImage {
  // 为每个图片设置不同的seed
  faker.seed(id * 1000 + Date.now() % 1000)
  const now = faker.date.recent()
  const attributes: Record<string, string> = {}

  // 为每个taggingAttribute生成mock值
  taggingAttributes.forEach(attr => {
    // 跳过一些不需要mock的字段
    if (attr.key === 'image_path') {
      attributes[attr.key] = `https://picsum.photos/seed/${id}/400/300`
      return
    }

    const mockValue = generateMockValue(attr)
    if (mockValue) {
      attributes[attr.key] = mockValue
    }
  })

  return {
    id,
    url: `https://picsum.photos/seed/${id}/400/300`,
    created_at: now.toISOString(),
    updated_at: now.toISOString(),
    attributes
  }
}

/**
 * 生成SQL插入语句
 */
function generateSQL(images: MockImage[]): string {
  const now = new Date().toISOString()
  let sql = `-- Generated mock data with comprehensive taggingAttributes coverage
-- Generated at: ${now}
-- Total images: ${images.length}
-- Total attributes covered: ${taggingAttributes.length}

-- Insert images
INSERT INTO tagging_image (`url`, `created_at`, `updated_at`) VALUES\n`

  // 生成图片插入语句
  const imageValues = images.map(img => 
    `('${img.url}', '${img.created_at}', '${img.updated_at}')`
  ).join(',\n')
  
  sql += imageValues + ';\n\n'
  
  // 生成属性插入语句
  sql += '-- Insert tagging values\n'
  sql += 'INSERT INTO tagging_value (`image_id`, `key`, `value`, `created_at`, `updated_at`) VALUES\n'
  
  const valueStatements: string[] = []
  
  images.forEach(img => {
    Object.entries(img.attributes).forEach(([key, value]) => {
      // 转义单引号
      const escapedValue = value.replace(/'/g, "''")
      valueStatements.push(
        `(${img.id}, '${key}', '${escapedValue}', '${img.created_at}', '${img.updated_at}')`
      )
    })
  })
  
  sql += valueStatements.join(',\n') + ';'
  
  return sql
}

/**
 * 主函数
 */
async function generateMockData() {
  try {
    console.log('🎭 Generating comprehensive mock data...')
    console.log(`📊 Total attributes to cover: ${taggingAttributes.length}`)
    
    // 生成20张图片的mock数据
    const images: MockImage[] = []
    for (let i = 1; i <= 20; i++) {
      images.push(generateMockImage(i))
    }
    
    console.log(`🖼️  Generated ${images.length} mock images`)
    
    // 统计覆盖的属性
    const coveredAttributes = new Set<string>()
    images.forEach(img => {
      Object.keys(img.attributes).forEach(key => coveredAttributes.add(key))
    })
    
    console.log(`✅ Covered ${coveredAttributes.size} out of ${taggingAttributes.length} attributes`)
    
    // 生成SQL
    const sql = generateSQL(images)
    
    // 写入文件
    const outputPath = path.join(__dirname, '..', 'mock-data-comprehensive.sql')
    await fs.writeFile(outputPath, sql, 'utf8')
    
    console.log(`📄 SQL file generated: ${outputPath}`)
    
    // 生成JSON格式的数据用于调试
    const jsonPath = path.join(__dirname, '..', 'mock-data-comprehensive.json')
    await fs.writeFile(jsonPath, JSON.stringify(images, null, 2), 'utf8')
    
    console.log(`📄 JSON file generated: ${jsonPath}`)
    
    // 显示未覆盖的属性
    const uncoveredAttributes = taggingAttributes
      .filter(attr => !coveredAttributes.has(attr.key))
      .map(attr => attr.key)
    
    if (uncoveredAttributes.length > 0) {
      console.log(`⚠️  Uncovered attributes (${uncoveredAttributes.length}):`)
      uncoveredAttributes.forEach(key => console.log(`   - ${key}`))
    }
    
    console.log('🎉 Mock data generation completed!')
    
  } catch (error) {
    console.error('❌ Error generating mock data:', error)
    process.exit(1)
  }
}

generateMockData()
