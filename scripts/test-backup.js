#!/usr/bin/env node

/**
 * 测试备份功能的脚本
 * 这个脚本会创建一个测试数据库并验证备份功能
 */

import { exec } from 'child_process';
import { promises as fs } from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const execAsync = promisify(exec);

async function testBackupFunction() {
  try {
    console.log('🧪 Testing backup functionality...');
    
    // 创建测试数据库
    const testDbPath = path.join(__dirname, '..', 'test.db');
    const testSql = `
      CREATE TABLE test_table (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
      INSERT INTO test_table (name) VALUES ('test1'), ('test2');
    `;
    
    // 创建测试数据库
    await execAsync(`echo "${testSql}" | sqlite3 "${testDbPath}"`);
    console.log('✅ Test database created');
    
    // 测试备份功能
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const backupFileName = `test_migration_backup_${timestamp}.sql`;
    const backupPath = path.join(__dirname, '..', 'src', 'backups', backupFileName);
    
    // 确保备份目录存在
    const backupDir = path.dirname(backupPath);
    await fs.mkdir(backupDir, { recursive: true });
    
    // 创建备份
    const command = `sqlite3 "${testDbPath}" ".dump" > "${backupPath}"`;
    await execAsync(command);
    console.log('✅ Backup created successfully');
    
    // 验证备份文件
    const backupContent = await fs.readFile(backupPath, 'utf8');
    if (backupContent.includes('CREATE TABLE test_table') && backupContent.includes('test1')) {
      console.log('✅ Backup content verified');
    } else {
      throw new Error('Backup content verification failed');
    }
    
    // 清理测试文件
    await fs.unlink(testDbPath);
    await fs.unlink(backupPath);
    console.log('✅ Test files cleaned up');
    
    console.log('🎉 Backup functionality test passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testBackupFunction();
