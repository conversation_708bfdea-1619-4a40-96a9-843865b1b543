# Mock Data Generator

这个脚本用于生成覆盖所有 `taggingAttributes` 字段的模拟数据，使用 Faker.js 生成真实的模拟数据。

## 功能特性

- ✅ **完整覆盖**: 覆盖所有91个 taggingAttributes 字段
- 🎭 **智能生成**: 根据字段类型和枚举值智能生成合适的mock数据
- 🖼️ **图片URL**: 使用 `https://picsum.photos/seed/${id}/400/300` 格式生成图片URL
- 📊 **多格式输出**: 同时生成SQL和JSON格式的数据
- 🔄 **随机性**: 每次运行生成不同的数据

## 使用方法

### 安装依赖

```bash
pnpm add -D @faker-js/faker
```

### 运行脚本

```bash
# 使用npm脚本
pnpm generate-mock

# 或直接运行
npx tsx scripts/generate-mock-data.ts
```

## 输出文件

脚本会在项目根目录生成两个文件：

### 1. `mock-data-comprehensive.sql`
- 包含完整的SQL INSERT语句
- 可直接导入数据库
- 包含20张图片和对应的标注数据

### 2. `mock-data-comprehensive.json`
- JSON格式的数据，便于调试和查看
- 结构化的数据格式，易于程序处理

## 数据结构

### 图片数据
```sql
INSERT INTO tagging_image (url, created_at, updated_at) VALUES
('https://picsum.photos/seed/1/400/300', '2025-07-03T08:34:33.960Z', '2025-07-03T08:34:33.960Z'),
...
```

### 标注数据
```sql
INSERT INTO tagging_value (image_id, key, value, created_at, updated_at) VALUES
(1, 'analysis.Model.Region', 'Chinese', '2025-07-03T08:34:33.960Z', '2025-07-03T08:34:33.960Z'),
(1, 'analysis.Model.Gender', 'Male', '2025-07-03T08:34:33.960Z', '2025-07-03T08:34:33.960Z'),
...
```

## 覆盖的属性类别

脚本覆盖以下主要类别的属性：

### 🧑 模特特征
- 地域、性别、年龄
- 发型（长度、卷曲度、颜色、发量、刘海、造型）
- 妆容、眼神、体型、肤色、姿态、头部位置、模特类型

### 👕 服装分析
- 上装（风格、长度、版型、领口、袖型、口袋、装饰、材质、颜色、图案、特征）
- 下装（风格、腰线、长度、版型、材质、颜色、图案、特征）
- 叠穿单品、鞋子、配饰、其他重要细节

### 📸 拍摄主题
- 用途、场景、背景
- 光线（角度、色温、曝光、氛围）
- 水印分析

### 🎨 镜头语言
- 构图、视角、景深

### 🎭 风格分析
- 一级分类（成熟商务、阳光青春、性感等）
- 二级分类（具体风格细分）
- 核心特征（色彩体系、剪裁轮廓、材质纹理、场景氛围、标志性元素）
- 图片总览

### 📊 系统字段
- 状态、时间戳、总结、总处理时间

## 智能数据生成

脚本根据字段特征智能生成数据：

- **枚举字段**: 从预定义的枚举值中随机选择
- **颜色字段**: 生成常见颜色名称
- **材质字段**: 生成常见服装材质
- **时间字段**: 生成ISO格式时间戳
- **描述字段**: 生成相关的描述性文本

## 自定义配置

可以通过修改 `scripts/generate-mock-data.ts` 文件来：

- 调整生成的图片数量
- 修改mock数据生成逻辑
- 添加新的字段类型处理
- 自定义输出格式

## 导入数据库

生成的SQL文件可以直接导入数据库：

```bash
# SQLite
sqlite3 your_database.db < mock-data-comprehensive.sql

# MySQL
mysql -u username -p database_name < mock-data-comprehensive.sql

# PostgreSQL
psql -U username -d database_name -f mock-data-comprehensive.sql
```

## 注意事项

- 确保数据库表结构已创建
- 生成的数据仅用于测试和开发
- 每次运行会覆盖之前生成的文件
- 图片URL使用外部服务，需要网络连接才能显示
