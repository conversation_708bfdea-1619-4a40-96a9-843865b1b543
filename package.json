{"name": "mg-data-label", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "start": "vite --port 3000", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run"}, "dependencies": {"@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-router": "^1.121.2", "@tanstack/react-router-devtools": "^1.121.2", "@tanstack/react-table": "^8.21.3", "@tanstack/router-devtools": "^1.123.2", "@tanstack/router-plugin": "^1.121.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "kysely": "^0.28.2", "lucide-react": "^0.525.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/node": "^24.0.8", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "tailwindcss": "^4.1.11", "typescript": "^5.7.2", "vite": "^7.0.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}