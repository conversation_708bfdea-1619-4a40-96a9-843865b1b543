CREATE TABLE `tagging_image` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    `url` VARCHAR(255) NOT NULL COMMENT '图片URL',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP NULL DEFAULT NULL COMMENT '删除时间'
) COMMENT='图片表';

CREATE TABLE `tagging_value` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    `image_id` INT NOT NULL COMMENT '图片ID',
    `key` VARCHAR(255) NOT NULL COMMENT '标签键',
    `value` VARCHAR(255) NOT NULL COMMENT '标签值',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (`image_id`) REFERENCES `tagging_image`(`id`)
) COMMENT='标签值表';
